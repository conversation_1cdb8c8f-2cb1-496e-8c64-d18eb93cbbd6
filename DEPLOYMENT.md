# Clinical Metabolomics Oracle - Deployment Guide

This document provides instructions for deploying the Clinical Metabolomics Oracle application using Docker and GitHub Actions.

## Prerequisites

- Docker and Docker Compose installed on the production server
- GitHub repository with Actions enabled
- Production server with SSH access

## GitHub Secrets Configuration

The following secrets must be configured in your GitHub repository settings for automated deployment:

### Required Secrets

| Secret Name | Description | Example |
|-------------|-------------|---------|
| `PRODUCTION_HOST` | Production server hostname or IP | `your-server.com` |
| `PRODUCTION_USER` | SSH username for deployment | `deploy` |
| `PRODUCTION_SSH_KEY` | Private SSH key for server access | `-----BEGIN OPENSSH PRIVATE KEY-----...` |
| `PRODUCTION_PORT` | SSH port (optional, defaults to 22) | `22` |
| `PRODUCTION_PROJECT_PATH` | Project directory on server | `/opt/cmo_fastapi_react` |

### Optional Secrets

| Secret Name | Description | Default |
|-------------|-------------|---------|
| `OPENAI_API_KEY` | OpenAI API key for production | Required for chat functionality |

## Production Server Setup

### 1. Create Deployment User

```bash
# Create deployment user
sudo useradd -m -s /bin/bash deploy
sudo usermod -aG docker deploy

# Set up SSH key authentication
sudo mkdir -p /home/<USER>/.ssh
sudo chown deploy:deploy /home/<USER>/.ssh
sudo chmod 700 /home/<USER>/.ssh

# Add your public key to authorized_keys
sudo nano /home/<USER>/.ssh/authorized_keys
sudo chown deploy:deploy /home/<USER>/.ssh/authorized_keys
sudo chmod 600 /home/<USER>/.ssh/authorized_keys
```

### 2. Clone Repository

```bash
# Switch to deploy user
sudo su - deploy

# Clone the repository
git clone https://github.com/your-username/cmo_fastapi_react.git /opt/cmo_fastapi_react
cd /opt/cmo_fastapi_react

# Create production environment file
cp .env.example .env
nano .env  # Configure production values
```

### 3. Configure Environment Variables

Edit the `.env` file with production values:

```bash
# Database Configuration
POSTGRES_USER=postgres
POSTGRES_PASSWORD=your-secure-password
POSTGRES_DB=cmo_db

# Backend Configuration
SECRET_KEY=your-very-long-and-secure-secret-key
ENVIRONMENT=production

# OpenAI API Configuration
OPENAI_API_KEY=your-openai-api-key
```

## Manual Deployment

For manual deployment or initial setup:

```bash
# Build and start services
docker-compose up -d --build

# Check service status
docker-compose ps

# View logs
docker-compose logs -f

# Run database migrations (if needed)
docker-compose exec backend alembic upgrade head
```

## Automated Deployment

The CI/CD pipeline automatically:

1. **Continuous Integration (CI)**:
   - Runs on every push and pull request
   - Tests backend with Python/pytest
   - Tests frontend with Node.js/npm
   - Runs linting and code quality checks

2. **Continuous Deployment (CD)**:
   - Triggers on pushes to `main` branch
   - Builds Docker images for backend and frontend
   - Pushes images to GitHub Container Registry
   - Deploys to production server via SSH

## Monitoring and Maintenance

### Health Checks

- Backend: `http://your-server:8000/health`
- Frontend: `http://your-server:3000/health`

### Log Management

```bash
# View application logs
docker-compose logs -f backend
docker-compose logs -f frontend

# View system logs
journalctl -u docker
```

### Backup

```bash
# Backup database
docker-compose exec postgres pg_dump -U postgres cmo_db > backup_$(date +%Y%m%d_%H%M%S).sql

# Backup volumes
docker run --rm -v cmo_postgres_data:/data -v $(pwd):/backup alpine tar czf /backup/postgres_backup_$(date +%Y%m%d_%H%M%S).tar.gz /data
```

## Troubleshooting

### Common Issues

1. **Permission Denied**: Ensure deploy user is in docker group
2. **Port Conflicts**: Check if ports 3000/8000 are available
3. **Database Connection**: Verify PostgreSQL is running and accessible
4. **Image Pull Errors**: Check GitHub Container Registry authentication

### Useful Commands

```bash
# Restart services
docker-compose restart

# Rebuild specific service
docker-compose up -d --build backend

# Clean up old images
docker image prune -f

# Check resource usage
docker stats
```

## Security Considerations

- Use strong passwords for database and secret keys
- Keep SSH keys secure and rotate regularly
- Enable firewall and limit SSH access
- Regularly update Docker images and dependencies
- Monitor logs for suspicious activity
- Use HTTPS in production with proper SSL certificates

## Support

For deployment issues, check:
1. GitHub Actions logs
2. Server logs (`docker-compose logs`)
3. System logs (`journalctl`)
4. Network connectivity and firewall settings
