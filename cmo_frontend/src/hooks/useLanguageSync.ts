import { useEffect } from 'react'
import { useSelector } from 'react-redux'
import { useTranslation } from 'react-i18next'
import type { RootState } from '@/app/store'

/**
 * Custom hook to synchronize user's preferred language with i18next
 * 
 * This hook:
 * 1. Monitors changes to the authenticated user
 * 2. Updates i18next language when user logs in with a preferred language
 * 3. Handles language initialization on app startup for authenticated users
 */
export const useLanguageSync = () => {
  const { i18n } = useTranslation()
  const { user, isAuthenticated } = useSelector((state: RootState) => state.auth)

  useEffect(() => {
    const syncLanguage = async () => {
      // Only sync if user is authenticated and has a preferred language
      if (isAuthenticated && user?.preferred_language) {
        const userLanguage = user.preferred_language
        const currentLanguage = i18n.language

        // Only change language if it's different from current
        if (userLanguage !== currentLanguage) {
          try {
            await i18n.changeLanguage(userLanguage)
            console.log(`Language synchronized to user preference: ${userLanguage}`)
          } catch (error) {
            console.error('Failed to sync language from user profile:', error)
          }
        }
      }
    }

    syncLanguage()
  }, [user, isAuthenticated, i18n])

  // Return current language info for debugging/monitoring
  return {
    currentLanguage: i18n.language,
    userPreferredLanguage: user?.preferred_language,
    isAuthenticated
  }
}
