import { useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useSelector } from 'react-redux'
import { RootState } from '@/app/store'
import apiClient from '@/api/client'

interface Language {
  code: string
  name: string
  flag: string
}

const languages: Language[] = [
  { code: 'en', name: 'English', flag: '🇺🇸' },
  { code: 'es', name: 'Español', flag: '🇪🇸' }
]

interface LanguageSwitcherProps {
  variant?: 'dropdown' | 'buttons'
  size?: 'small' | 'medium' | 'large'
  showFlags?: boolean
}

const LanguageSwitcher = ({
  variant = 'dropdown',
  size = 'medium',
  showFlags = true
}: LanguageSwitcherProps) => {
  const { i18n, t } = useTranslation()
  const [isOpen, setIsOpen] = useState(false)
  const [isUpdating, setIsUpdating] = useState(false)

  const { isAuthenticated } = useSelector((state: RootState) => state.auth)
  const currentLanguage = languages.find(lang => lang.code === i18n.language) || languages[0]

  const handleLanguageChange = async (languageCode: string) => {
    if (isUpdating) return

    try {
      setIsUpdating(true)

      // Change language in i18next
      await i18n.changeLanguage(languageCode)

      // Save language preference to user profile if authenticated
      if (isAuthenticated) {
        try {
          await apiClient.patch('/users/me', {
            preferred_language: languageCode
          })
        } catch (error) {
          console.error('Failed to save language preference:', error)
          // Language change still works locally even if API call fails
        }
      }

      setIsOpen(false)
    } catch (error) {
      console.error('Failed to change language:', error)
    } finally {
      setIsUpdating(false)
    }
  }

  const getSizeStyles = () => {
    switch (size) {
      case 'small':
        return {
          fontSize: '0.8rem',
          padding: '0.25rem 0.5rem'
        }
      case 'large':
        return {
          fontSize: '1.1rem',
          padding: '0.75rem 1rem'
        }
      default:
        return {
          fontSize: '0.9rem',
          padding: '0.5rem 0.75rem'
        }
    }
  }

  if (variant === 'buttons') {
    return (
      <div style={{
        display: 'flex',
        gap: '0.5rem',
        alignItems: 'center'
      }}>
        <span style={{ 
          fontSize: '0.8rem', 
          color: '#666',
          marginRight: '0.25rem'
        }}>
          {t('language.switchLanguage')}:
        </span>
        {languages.map((language) => (
          <button
            key={language.code}
            onClick={() => handleLanguageChange(language.code)}
            disabled={isUpdating}
            style={{
              ...getSizeStyles(),
              backgroundColor: i18n.language === language.code ? '#007bff' : 'transparent',
              color: i18n.language === language.code ? 'white' : '#007bff',
              border: '1px solid #007bff',
              borderRadius: '4px',
              cursor: isUpdating ? 'not-allowed' : 'pointer',
              display: 'flex',
              alignItems: 'center',
              gap: '0.25rem',
              transition: 'all 0.2s ease',
              opacity: isUpdating ? 0.6 : 1
            }}
            onMouseEnter={(e) => {
              if (i18n.language !== language.code) {
                e.currentTarget.style.backgroundColor = '#f8f9fa'
              }
            }}
            onMouseLeave={(e) => {
              if (i18n.language !== language.code) {
                e.currentTarget.style.backgroundColor = 'transparent'
              }
            }}
          >
            {showFlags && <span>{language.flag}</span>}
            <span>{language.name}</span>
          </button>
        ))}
      </div>
    )
  }

  // Dropdown variant
  return (
    <div style={{ position: 'relative', display: 'inline-block' }}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        disabled={isUpdating}
        style={{
          ...getSizeStyles(),
          backgroundColor: 'white',
          border: '1px solid #ddd',
          borderRadius: '6px',
          cursor: isUpdating ? 'not-allowed' : 'pointer',
          display: 'flex',
          alignItems: 'center',
          gap: '0.5rem',
          minWidth: '120px',
          justifyContent: 'space-between',
          transition: 'all 0.2s ease',
          opacity: isUpdating ? 0.6 : 1
        }}
        onMouseEnter={(e) => {
          e.currentTarget.style.borderColor = '#007bff'
          e.currentTarget.style.boxShadow = '0 0 0 2px rgba(0,123,255,0.1)'
        }}
        onMouseLeave={(e) => {
          e.currentTarget.style.borderColor = '#ddd'
          e.currentTarget.style.boxShadow = 'none'
        }}
      >
        <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
          {showFlags && <span>{currentLanguage.flag}</span>}
          <span>{currentLanguage.name}</span>
        </div>
        <span style={{ 
          transform: isOpen ? 'rotate(180deg)' : 'rotate(0deg)',
          transition: 'transform 0.2s ease'
        }}>
          ▼
        </span>
      </button>

      {isOpen && (
        <>
          {/* Backdrop to close dropdown when clicking outside */}
          <div
            style={{
              position: 'fixed',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              zIndex: 999
            }}
            onClick={() => setIsOpen(false)}
          />
          
          {/* Dropdown menu */}
          <div style={{
            position: 'absolute',
            top: '100%',
            left: 0,
            right: 0,
            backgroundColor: 'white',
            border: '1px solid #ddd',
            borderRadius: '6px',
            boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
            zIndex: 1000,
            marginTop: '2px',
            overflow: 'hidden'
          }}>
            {languages.map((language) => (
              <button
                key={language.code}
                onClick={() => handleLanguageChange(language.code)}
                disabled={isUpdating}
                style={{
                  width: '100%',
                  padding: '0.75rem',
                  backgroundColor: i18n.language === language.code ? '#f8f9fa' : 'white',
                  border: 'none',
                  cursor: isUpdating ? 'not-allowed' : 'pointer',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.5rem',
                  fontSize: '0.9rem',
                  transition: 'background-color 0.2s ease',
                  textAlign: 'left',
                  opacity: isUpdating ? 0.6 : 1
                }}
                onMouseEnter={(e) => {
                  if (i18n.language !== language.code) {
                    e.currentTarget.style.backgroundColor = '#f8f9fa'
                  }
                }}
                onMouseLeave={(e) => {
                  if (i18n.language !== language.code) {
                    e.currentTarget.style.backgroundColor = 'white'
                  }
                }}
              >
                {showFlags && <span>{language.flag}</span>}
                <span>{language.name}</span>
                {i18n.language === language.code && (
                  <span style={{ marginLeft: 'auto', color: '#007bff' }}>✓</span>
                )}
              </button>
            ))}
          </div>
        </>
      )}
    </div>
  )
}

export default LanguageSwitcher
