import { createSlice, createAsyncThunk, type PayloadAction } from '@reduxjs/toolkit'

export interface ChatMessage {
  id?: number
  content: string
  sender: 'user' | 'ai'
  timestamp: string
  thread_id?: number
}

export interface ChatThread {
  id: number
  title: string
  created_at: string
  updated_at?: string
  messages?: ChatMessage[]
}

export interface ChatState {
  messages: ChatMessage[]
  currentThread: ChatThread | null
  threads: ChatThread[]
  status: 'idle' | 'loading' | 'succeeded' | 'failed'
  currentStatus: string | null
  error: string | null
}

const initialState: ChatState = {
  messages: [],
  currentThread: null,
  threads: [],
  status: 'idle',
  currentStatus: null,
  error: null,
}

export interface SendMessagePayload {
  message: string
  thread_id?: number
}

export interface AppendMessageChunkPayload {
  chunk: string
}

export interface SetChatStatusPayload {
  status: string
}

// Async thunk for sending a message (just for state management, not HTTP request)
export const sendMessage = createAsyncThunk(
  'chat/sendMessage',
  async (payload: SendMessagePayload) => {
    // This thunk is just for state management
    // The actual HTTP request and streaming will be handled in the component
    return payload
  }
)

const chatSlice = createSlice({
  name: 'chat',
  initialState,
  reducers: {
    // Add user message to the messages array
    addUserMessage: (state, action: PayloadAction<ChatMessage>) => {
      state.messages.push(action.payload)
    },
    
    // Start a new AI message (empty content that will be filled by chunks)
    startAiMessage: (state) => {
      const aiMessage: ChatMessage = {
        content: '',
        sender: 'ai',
        timestamp: new Date().toISOString(),
      }
      state.messages.push(aiMessage)
    },
    
    // Append chunk to the last AI message
    appendAiMessageChunk: (state, action: PayloadAction<AppendMessageChunkPayload>) => {
      const lastMessage = state.messages[state.messages.length - 1]
      if (lastMessage && lastMessage.sender === 'ai') {
        lastMessage.content += action.payload.chunk
      }
    },
    
    // Mark message stream as complete
    messageStreamComplete: (state) => {
      state.status = 'succeeded'
      state.currentStatus = null
    },
    
    // Set current chat status (for displaying "Thinking...", "Querying database...", etc.)
    setChatStatus: (state, action: PayloadAction<SetChatStatusPayload>) => {
      state.currentStatus = action.payload.status
    },
    
    // Clear current status
    clearChatStatus: (state) => {
      state.currentStatus = null
    },
    
    // Set current thread
    setCurrentThread: (state, action: PayloadAction<ChatThread>) => {
      state.currentThread = action.payload
      state.messages = action.payload.messages || []
    },
    
    // Clear current chat
    clearCurrentChat: (state) => {
      state.messages = []
      state.currentThread = null
      state.status = 'idle'
      state.currentStatus = null
      state.error = null
    },
    
    // Set threads list
    setThreads: (state, action: PayloadAction<ChatThread[]>) => {
      state.threads = action.payload
    },
    
    // Set error
    setError: (state, action: PayloadAction<string>) => {
      state.error = action.payload
      state.status = 'failed'
      state.currentStatus = null
    },
    
    // Clear error
    clearError: (state) => {
      state.error = null
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(sendMessage.pending, (state) => {
        state.status = 'loading'
        state.error = null
        state.currentStatus = 'Sending message...'
      })
      .addCase(sendMessage.fulfilled, (state, action) => {
        // Add user message to state
        const userMessage: ChatMessage = {
          content: action.payload.message,
          sender: 'user',
          timestamp: new Date().toISOString(),
        }
        state.messages.push(userMessage)
        
        // Start AI message placeholder
        const aiMessage: ChatMessage = {
          content: '',
          sender: 'ai',
          timestamp: new Date().toISOString(),
        }
        state.messages.push(aiMessage)
        
        state.currentStatus = 'Waiting for response...'
      })
      .addCase(sendMessage.rejected, (state, action) => {
        state.status = 'failed'
        state.error = action.payload as string
        state.currentStatus = null
      })
  },
})

export const {
  addUserMessage,
  startAiMessage,
  appendAiMessageChunk,
  messageStreamComplete,
  setChatStatus,
  clearChatStatus,
  setCurrentThread,
  clearCurrentChat,
  setThreads,
  setError,
  clearError,
} = chatSlice.actions

export default chatSlice.reducer
