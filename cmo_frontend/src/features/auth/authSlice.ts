import { createSlice, type PayloadAction } from '@reduxjs/toolkit'

export interface User {
  id: number
  email: string
  role: 'user' | 'admin'
  preferred_language: string
}

export interface AuthState {
  user: User | null
  isAuthenticated: boolean
  accessToken: string | null
  refreshToken: string | null
}

const initialState: AuthState = {
  user: null,
  isAuthenticated: false,
  accessToken: null,
  refreshToken: null,
}

export interface LoginSuccessPayload {
  user: User
  accessToken: string
  refreshToken: string
}

export interface TokensRefreshedPayload {
  accessToken: string
}

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    loginSuccess: (state, action: PayloadAction<LoginSuccessPayload>) => {
      state.user = action.payload.user
      state.isAuthenticated = true
      state.accessToken = action.payload.accessToken
      state.refreshToken = action.payload.refreshToken
    },
    logoutSuccess: (state) => {
      state.user = null
      state.isAuthenticated = false
      state.accessToken = null
      state.refreshToken = null
    },
    tokensRefreshed: (state, action: PayloadAction<TokensRefreshedPayload>) => {
      state.accessToken = action.payload.accessToken
    },
  },
})

export const { loginSuccess, logoutSuccess, tokensRefreshed } = authSlice.actions
export default authSlice.reducer
