import { useState, useEffect, useRef } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { useSearchParams, Link } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import { RootState, AppDispatch } from '@/app/store'
import LanguageSwitcher from '@/components/LanguageSwitcher'
import {
  sendMessage,
  appendAiMessageChunk,
  setChatStatus,
  clearChatStatus,
  messageStreamComplete,
  setError,
  setCurrentThread,
  clearCurrentChat
} from '@/features/chat/chatSlice'
import apiClient from '@/api/client'

const ChatPage = () => {
  const { t } = useTranslation()
  const [inputMessage, setInputMessage] = useState('')
  const [isLoadingThread, setIsLoadingThread] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const eventSourceRef = useRef<EventSource | null>(null)
  const [searchParams] = useSearchParams()

  const dispatch = useDispatch<AppDispatch>()
  const { messages, status, currentStatus, error, currentThread } = useSelector((state: RootState) => state.chat)
  const { accessToken } = useSelector((state: RootState) => state.auth)

  // Auto-scroll to bottom when new messages arrive
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  // Load specific thread if thread parameter is provided
  useEffect(() => {
    const threadId = searchParams.get('thread')
    if (threadId && accessToken) {
      loadThread(parseInt(threadId))
    } else {
      // Clear current chat if no thread specified
      dispatch(clearCurrentChat())
    }
  }, [searchParams, accessToken, dispatch])

  const loadThread = async (threadId: number) => {
    try {
      setIsLoadingThread(true)
      const response = await apiClient.get(`/chat/history/${threadId}`)
      dispatch(setCurrentThread(response.data))
    } catch (err: any) {
      dispatch(setError(err.response?.data?.detail || t('chat.status.loadError')))
    } finally {
      setIsLoadingThread(false)
    }
  }

  // Handle streaming response after sendMessage is dispatched
  useEffect(() => {
    if (status === 'loading' && accessToken && messages.length >= 2) {
      // Get the last user message to send to the backend
      const lastUserMessage = messages[messages.length - 2] // -2 because we already added AI placeholder
      if (lastUserMessage && lastUserMessage.sender === 'user') {
        handleStreamingResponse(lastUserMessage.content)
      }
    }
  }, [status, accessToken, messages, dispatch])

  const handleStreamingResponse = async (messageContent: string) => {
    try {
      const requestBody: any = { message: messageContent }
      if (currentThread?.id) {
        requestBody.thread_id = currentThread.id
      }

      const response = await fetch('http://localhost:8000/api/v1/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${accessToken}`,
        },
        body: JSON.stringify(requestBody),
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      if (!response.body) {
        throw new Error('No response body')
      }

      const reader = response.body.getReader()
      const decoder = new TextDecoder()

      while (true) {
        const { done, value } = await reader.read()

        if (done) {
          dispatch(messageStreamComplete())
          dispatch(clearChatStatus())
          break
        }

        const chunk = decoder.decode(value, { stream: true })
        const lines = chunk.split('\n')

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6))

              if (data.type === 'token') {
                dispatch(appendAiMessageChunk({ chunk: data.content }))
              } else if (data.type === 'status') {
                dispatch(setChatStatus({ status: data.message }))
              } else if (data.type === 'complete') {
                dispatch(messageStreamComplete())
                dispatch(clearChatStatus())
                return
              } else if (data.type === 'error') {
                dispatch(setError(data.message))
                return
              }
            } catch (parseError) {
              console.error('Error parsing SSE data:', parseError)
            }
          }
        }
      }
    } catch (error) {
      console.error('Streaming error:', error)
      dispatch(setError(error instanceof Error ? error.message : t('errors.generic')))
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!inputMessage.trim() || status === 'loading') {
      return
    }

    const messageToSend = inputMessage.trim()
    setInputMessage('')

    // Dispatch the sendMessage thunk with thread_id if available
    dispatch(sendMessage({
      message: messageToSend,
      thread_id: currentThread?.id
    }))
  }

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit' 
    })
  }

  return (
    <div style={{
      height: '100vh',
      display: 'flex',
      flexDirection: 'column',
      backgroundColor: '#f5f5f5'
    }}>
      {/* Header */}
      <div style={{
        backgroundColor: 'white',
        padding: '1rem 2rem',
        borderBottom: '1px solid #e0e0e0',
        boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      }}>
        <div>
          <h1 style={{ margin: 0, color: '#333' }}>
            {currentThread ? currentThread.title : t('chat.title')}
          </h1>
          <p style={{ margin: '0.5rem 0 0 0', color: '#666', fontSize: '0.9rem' }}>
            {currentThread ? t('chat.continueConversation') : t('chat.subtitle')}
          </p>
        </div>
        <div style={{ display: 'flex', gap: '1rem', alignItems: 'center' }}>
          <LanguageSwitcher size="small" />
          <Link
            to="/history"
            style={{
              padding: '0.5rem 1rem',
              backgroundColor: '#f8f9fa',
              color: '#007bff',
              border: '1px solid #007bff',
              borderRadius: '6px',
              textDecoration: 'none',
              fontSize: '0.9rem'
            }}
          >
            {t('navigation.history')}
          </Link>
          {currentThread && (
            <Link
              to="/chat"
              style={{
                padding: '0.5rem 1rem',
                backgroundColor: '#007bff',
                color: 'white',
                border: 'none',
                borderRadius: '6px',
                textDecoration: 'none',
                fontSize: '0.9rem'
              }}
            >
              {t('chat.newChat')}
            </Link>
          )}
        </div>
      </div>

      {/* Messages Area */}
      <div style={{
        flex: 1,
        overflowY: 'auto',
        padding: '1rem',
        display: 'flex',
        flexDirection: 'column',
        gap: '1rem'
      }}>
        {isLoadingThread && (
          <div style={{
            textAlign: 'center',
            color: '#666',
            marginTop: '2rem'
          }}>
            <h3>{t('chat.status.loading')}</h3>
            <p>{t('chat.status.loadingMessage')}</p>
          </div>
        )}

        {!isLoadingThread && messages.length === 0 && (
          <div style={{
            textAlign: 'center',
            color: '#666',
            marginTop: '2rem'
          }}>
            <h3>{t('chat.welcome.title')}</h3>
            <p>{t('chat.welcome.subtitle')}</p>
          </div>
        )}

        {messages.map((message, index) => (
          <div
            key={index}
            style={{
              display: 'flex',
              justifyContent: message.sender === 'user' ? 'flex-end' : 'flex-start',
              marginBottom: '0.5rem'
            }}
          >
            <div style={{
              maxWidth: '70%',
              padding: '0.75rem 1rem',
              borderRadius: '18px',
              backgroundColor: message.sender === 'user' ? '#007bff' : 'white',
              color: message.sender === 'user' ? 'white' : '#333',
              boxShadow: '0 1px 2px rgba(0,0,0,0.1)',
              border: message.sender === 'ai' ? '1px solid #e0e0e0' : 'none'
            }}>
              <div style={{ whiteSpace: 'pre-wrap', lineHeight: '1.4' }}>
                {message.content}
              </div>
              <div style={{
                fontSize: '0.75rem',
                opacity: 0.7,
                marginTop: '0.25rem',
                textAlign: message.sender === 'user' ? 'right' : 'left'
              }}>
                {formatTimestamp(message.timestamp)}
              </div>
            </div>
          </div>
        ))}

        {/* Status indicator */}
        {currentStatus && (
          <div style={{
            display: 'flex',
            justifyContent: 'flex-start',
            marginBottom: '0.5rem'
          }}>
            <div style={{
              padding: '0.5rem 1rem',
              backgroundColor: '#f0f0f0',
              borderRadius: '18px',
              color: '#666',
              fontSize: '0.9rem',
              fontStyle: 'italic'
            }}>
              {currentStatus}
            </div>
          </div>
        )}

        {/* Loading indicator */}
        {status === 'loading' && !currentStatus && (
          <div style={{
            display: 'flex',
            justifyContent: 'flex-start',
            marginBottom: '0.5rem'
          }}>
            <div style={{
              padding: '0.5rem 1rem',
              backgroundColor: '#f0f0f0',
              borderRadius: '18px',
              color: '#666',
              fontSize: '0.9rem'
            }}>
              <span>{t('chat.input.thinking')}</span>
              <span style={{ animation: 'pulse 1.5s infinite' }}>...</span>
            </div>
          </div>
        )}

        {/* Error display */}
        {error && (
          <div style={{
            backgroundColor: '#fee',
            color: '#c33',
            padding: '0.75rem',
            borderRadius: '8px',
            border: '1px solid #fcc'
          }}>
            {t('common.error')}: {error}
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* Input Area */}
      <div style={{
        backgroundColor: 'white',
        padding: '1rem 2rem',
        borderTop: '1px solid #e0e0e0'
      }}>
        <form onSubmit={handleSubmit} style={{ display: 'flex', gap: '0.5rem' }}>
          <input
            type="text"
            value={inputMessage}
            onChange={(e) => setInputMessage(e.target.value)}
            placeholder={t('chat.input.placeholder')}
            disabled={status === 'loading'}
            style={{
              flex: 1,
              padding: '0.75rem 1rem',
              border: '1px solid #ddd',
              borderRadius: '25px',
              fontSize: '1rem',
              outline: 'none',
              backgroundColor: status === 'loading' ? '#f5f5f5' : 'white'
            }}
          />
          <button
            type="submit"
            disabled={!inputMessage.trim() || status === 'loading'}
            style={{
              padding: '0.75rem 1.5rem',
              backgroundColor: '#007bff',
              color: 'white',
              border: 'none',
              borderRadius: '25px',
              fontSize: '1rem',
              cursor: (!inputMessage.trim() || status === 'loading') ? 'not-allowed' : 'pointer',
              opacity: (!inputMessage.trim() || status === 'loading') ? 0.6 : 1,
              minWidth: '80px'
            }}
          >
            {status === 'loading' ? '...' : t('common.send')}
          </button>
        </form>
      </div>

      {/* Add CSS animation for loading dots */}
      <style>{`
        @keyframes pulse {
          0%, 50% { opacity: 1; }
          100% { opacity: 0.3; }
        }
      `}</style>
    </div>
  )
}

export default ChatPage
