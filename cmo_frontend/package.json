{"name": "cmo_frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "npx vite", "build": "tsc -b && npx vite build", "lint": "eslint .", "preview": "npx vite preview", "test": "echo 'No tests specified yet' && exit 0"}, "dependencies": {"@reduxjs/toolkit": "^2.8.2", "axios": "^1.10.0", "i18next": "^25.2.1", "i18next-browser-languagedetector": "^8.2.0", "i18next-http-backend": "^3.0.2", "react": "^19.1.0", "react-dom": "^19.1.0", "react-i18next": "^15.5.3", "react-redux": "^9.2.0", "react-router-dom": "^7.6.2"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/node": "^24.0.1", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}}