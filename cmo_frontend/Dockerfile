# Multi-stage build for Clinical Metabolomics Oracle Frontend
# Stage 1: Builder stage for installing dependencies and building the app
FROM node:18-alpine as builder

# Set working directory
WORKDIR /app

# Set environment variables for Node.js
ENV NODE_ENV=production
ENV CI=true

# Install dependencies for building (if needed)
RUN apk add --no-cache \
    git \
    python3 \
    make \
    g++

# Copy package files
COPY package*.json ./

# Install all dependencies (including dev dependencies for building)
# Remove package-lock.json and node_modules to avoid platform-specific issues
RUN rm -rf package-lock.json node_modules && npm install --silent

# Explicitly install the ARM64 rollup package if needed
RUN npm install @rollup/rollup-linux-arm64-musl --save-optional --silent || true

# Copy source code
COPY . .

# Build the application
RUN npm run build

# Stage 2: Production stage with Nginx
FROM nginx:alpine as production

# Install curl for health checks
RUN apk add --no-cache curl

# Remove default nginx website
RUN rm -rf /usr/share/nginx/html/*

# Copy built application from builder stage
COPY --from=builder /app/dist /usr/share/nginx/html

# Copy custom nginx configuration
COPY nginx/nginx.conf /etc/nginx/nginx.conf

# Create nginx user and set permissions
RUN chown -R nginx:nginx /usr/share/nginx/html && \
    chown -R nginx:nginx /var/cache/nginx && \
    chown -R nginx:nginx /var/log/nginx && \
    chown -R nginx:nginx /etc/nginx/conf.d && \
    touch /tmp/nginx.pid && \
    chown nginx:nginx /tmp/nginx.pid

# Switch to nginx user
USER nginx

# Expose port 80
EXPOSE 80

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:80/ || exit 1

# Start nginx
CMD ["nginx", "-g", "daemon off;"]
