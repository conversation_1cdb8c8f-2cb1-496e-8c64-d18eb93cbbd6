# Clinical Metabolomics Oracle (CMO)

A sophisticated AI-powered system for clinical metabolomics research and analysis, built with FastAPI backend and React frontend.

## 🚀 Quick Start

### Prerequisites

- **Docker & Docker Compose** (recommended)
- **Python 3.11+** (for local development)
- **Node.js 18+** (for frontend development)
- **PostgreSQL 15+** (if running locally)
- **Redis 7+** (if running locally)

### Docker Setup (Recommended)

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd cmo_fastapi_react
   ```

2. **Create environment file**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Start all services**
   ```bash
   docker-compose up -d
   ```

4. **Access the application**
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:8000
   - API Documentation: http://localhost:8000/docs

## 📋 Table of Contents

- [Architecture Overview](#architecture-overview)
- [Installation Guide](#installation-guide)
- [Configuration](#configuration)
- [API Documentation](#api-documentation)
- [Frontend Guide](#frontend-guide)
- [Development](#development)
- [Deployment](#deployment)
- [Usage Guide](#usage-guide)
- [Troubleshooting](#troubleshooting)

## 📚 Complete Documentation

- **[📖 Installation Guide](./docs/INSTALLATION.md)** - Comprehensive setup instructions
- **[🔌 API Documentation](./docs/API.md)** - Complete API reference
- **[🎨 Frontend Development](./docs/FRONTEND.md)** - React development guide
- **[🚀 Deployment Guide](./docs/DEPLOYMENT.md)** - Production deployment
- **[👥 Usage Guide](./docs/USAGE.md)** - How to use the application
- **[🔧 Troubleshooting](./docs/TROUBLESHOOTING.md)** - Common issues and solutions

## 🏗️ Architecture Overview

### System Components

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   React Frontend│    │  FastAPI Backend│    │   PostgreSQL    │
│   (Port 3000)   │◄──►│   (Port 8000)   │◄──►│   (Port 5433)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐
                       │      Redis      │
                       │   (Port 6380)   │
                       └─────────────────┘
```

### Technology Stack

**Backend:**
- **FastAPI** - Modern Python web framework
- **SQLAlchemy** - ORM with async support
- **PostgreSQL** - Primary database
- **Redis** - Caching and session management
- **OpenAI API** - AI chat functionality
- **JWT** - Authentication tokens

**Frontend:**
- **React 19** - UI framework
- **TypeScript** - Type safety
- **Vite** - Build tool
- **Redux Toolkit** - State management
- **React Router** - Navigation
- **i18next** - Internationalization

**Infrastructure:**
- **Docker** - Containerization
- **Nginx** - Frontend web server
- **Gunicorn** - Python WSGI server

## 🛠️ Installation Guide

### Option 1: Docker Setup (Recommended)

#### Step 1: Environment Configuration

Create a `.env` file in the project root:

```bash
# Database Configuration
POSTGRES_USER=postgres
POSTGRES_PASSWORD=your_secure_password
POSTGRES_DB=cmo_db

# Security
SECRET_KEY=your-super-secret-key-change-in-production
JWT_SECRET_KEY=your-jwt-secret-key

# OpenAI Configuration (Required for AI features)
OPENAI_API_KEY=your-openai-api-key
CMO_ASSISTANT_ID=your-openai-assistant-id

# Optional: Custom ports
POSTGRES_PORT=5433
REDIS_PORT=6380
```

#### Step 2: Start Services

```bash
# Start all services in background
docker-compose up -d

# View logs
docker-compose logs -f

# Check service status
docker-compose ps
```

#### Step 3: Database Setup

```bash
# Run database migrations
docker-compose exec backend alembic upgrade head

# (Optional) Create admin user
docker-compose exec backend python -c "
from app.core.database import get_db
from app.crud.user import create_user
from app.models.user import UserCreate, UserRole
import asyncio

async def create_admin():
    async for db in get_db():
        admin_data = UserCreate(
            email='<EMAIL>',
            password='admin123',
            role=UserRole.ADMIN
        )
        await create_user(db, admin_data)
        print('Admin user created')

asyncio.run(create_admin())
"
```

### Option 2: Local Development Setup

#### Backend Setup

1. **Create Python virtual environment**
   ```bash
   cd cmo_backend
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Setup local database**
   ```bash
   # Install PostgreSQL and Redis locally
   # Create database
   createdb cmo_db
   ```

4. **Configure environment**
   ```bash
   cp .env.example .env
   # Edit .env with local database settings
   ```

5. **Run migrations**
   ```bash
   alembic upgrade head
   ```

6. **Start backend server**
   ```bash
   uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
   ```

#### Frontend Setup

1. **Install Node.js dependencies**
   ```bash
   cd cmo_frontend
   npm install
   ```

2. **Start development server**
   ```bash
   npm run dev
   ```

3. **Access application**
   - Frontend: http://localhost:5173
   - Backend: http://localhost:8000

## ⚙️ Configuration

### Environment Variables

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `POSTGRES_USER` | Database username | postgres | Yes |
| `POSTGRES_PASSWORD` | Database password | password | Yes |
| `POSTGRES_DB` | Database name | cmo_db | Yes |
| `SECRET_KEY` | Application secret key | auto-generated | Yes |
| `JWT_SECRET_KEY` | JWT signing key | auto-generated | Yes |
| `OPENAI_API_KEY` | OpenAI API key | - | Yes* |
| `CMO_ASSISTANT_ID` | OpenAI Assistant ID | - | Yes* |
| `ENVIRONMENT` | Environment mode | development | No |
| `ALLOWED_ORIGINS` | CORS origins | localhost:3000 | No |

*Required for AI chat functionality

### OpenAI Assistant Setup

1. **Create OpenAI Assistant**
   - Go to [OpenAI Platform](https://platform.openai.com)
   - Create a new Assistant
   - Configure with metabolomics knowledge
   - Copy the Assistant ID

2. **Configure Assistant**
   ```bash
   # Add to .env file
   OPENAI_API_KEY=sk-your-api-key
   CMO_ASSISTANT_ID=asst_your-assistant-id
   ```

## 📚 API Documentation

### Authentication Endpoints

#### Register User
```http
POST /auth/register
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "secure_password",
  "preferred_language": "en"
}
```

#### Login
```http
POST /auth/token
Content-Type: application/x-www-form-urlencoded

username=<EMAIL>&password=secure_password
```

#### Refresh Token
```http
POST /auth/refresh
Content-Type: application/json

{
  "refresh_token": "your_refresh_token"
}
```

### Chat Endpoints

#### Start Chat
```http
POST /chat/
Content-Type: application/json
Authorization: Bearer your_access_token

{
  "message": "What is metabolomics?",
  "thread_id": null
}
```

#### Get Chat History
```http
GET /chat/history?skip=0&limit=10
Authorization: Bearer your_access_token
```

#### Get Specific Thread
```http
GET /chat/history/{thread_id}
Authorization: Bearer your_access_token
```

### User Endpoints

#### Get Current User
```http
GET /users/me
Authorization: Bearer your_access_token
```

#### Update User Profile
```http
PUT /users/me
Content-Type: application/json
Authorization: Bearer your_access_token

{
  "preferred_language": "es"
}
```

For complete API documentation, visit: http://localhost:8000/docs

## 👥 Usage Guide

### Basic Usage

1. **Register/Login**
   - Create account at http://localhost:3000
   - Verify email and login

2. **Start Chatting**
   - Navigate to Chat interface
   - Ask questions about metabolomics
   - Example: "What is metabolomics and how is it used in clinical research?"

3. **Manage Conversations**
   - View chat history
   - Continue previous conversations
   - Export important discussions

### Example Questions

- **Research Planning**: "How should I design a metabolomics study for biomarker discovery?"
- **Data Analysis**: "What statistical methods are best for metabolomics data analysis?"
- **Clinical Applications**: "What are current clinical applications of metabolomics?"
- **Technical Questions**: "How do I normalize LC-MS metabolomics data?"

### Advanced Features

- **Multi-language Support**: Switch between English and Spanish
- **API Integration**: Use REST API for programmatic access
- **Data Export**: Download conversation history
- **Thread Management**: Organize conversations by topic

For detailed usage instructions, see: [Usage Guide](./docs/USAGE.md)

## 🎨 Frontend Guide

### Project Structure

```
cmo_frontend/
├── src/
│   ├── api/           # API client configuration
│   ├── app/           # App configuration (router, store)
│   ├── components/    # Reusable UI components
│   ├── features/      # Feature-specific components
│   │   ├── auth/      # Authentication components
│   │   └── chat/      # Chat components
│   ├── hooks/         # Custom React hooks
│   ├── pages/         # Page components
│   ├── styles/        # Global styles
│   └── lib/           # Utility functions
├── public/            # Static assets
└── dist/              # Build output
```

### Key Features

1. **Authentication System**
   - JWT-based authentication
   - Protected routes
   - Role-based access control

2. **Chat Interface**
   - Real-time streaming chat
   - Chat history management
   - Thread-based conversations

3. **Internationalization**
   - Multi-language support
   - Dynamic language switching
   - Persistent language preferences

4. **State Management**
   - Redux Toolkit for global state
   - Persistent authentication state
   - Optimistic UI updates

### Development Commands

```bash
# Start development server
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview

# Run linting
npm run lint

# Run tests (when implemented)
npm run test
```

## 🔧 Development

### Backend Development

#### Adding New API Endpoints

1. **Create model** in `app/models/`
2. **Create CRUD operations** in `app/crud/`
3. **Create API router** in `app/api/`
4. **Register router** in `app/main.py`

#### Database Migrations

```bash
# Create new migration
alembic revision --autogenerate -m "Description"

# Apply migrations
alembic upgrade head

# Rollback migration
alembic downgrade -1
```

#### Testing

```bash
# Run tests
pytest

# Run with coverage
pytest --cov=app tests/

# Run specific test
pytest tests/test_auth.py::test_register_user
```

### Frontend Development

#### Adding New Components

1. **Create component** in appropriate feature folder
2. **Add to router** if it's a page component
3. **Update state management** if needed
4. **Add translations** for i18n support

#### State Management

```typescript
// Define slice
const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {
    setUser: (state, action) => {
      state.currentUser = action.payload;
    }
  }
});

// Use in component
const user = useSelector((state: RootState) => state.user.currentUser);
const dispatch = useDispatch();
```

## 🚀 Deployment

### Production Docker Setup

1. **Create production environment file**
   ```bash
   cp .env.example .env.production
   # Configure with production values
   ```

2. **Build and deploy**
   ```bash
   # Build images
   docker-compose -f docker-compose.yml -f docker-compose.prod.yml build

   # Start services
   docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d
   ```

### Environment-Specific Configuration

- **Development**: Debug enabled, auto-reload, detailed logs
- **Production**: Optimized builds, security headers, log aggregation

## 🔍 Troubleshooting

### Common Issues

#### Backend Issues

**Database Connection Error**
```bash
# Check database status
docker-compose ps postgres

# View database logs
docker-compose logs postgres

# Reset database
docker-compose down -v
docker-compose up -d postgres
```

**OpenAI API Errors**
```bash
# Verify API key
echo $OPENAI_API_KEY

# Check assistant ID
curl -H "Authorization: Bearer $OPENAI_API_KEY" \
     https://api.openai.com/v1/assistants/$CMO_ASSISTANT_ID
```

#### Frontend Issues

**Build Failures**
```bash
# Clear node modules
rm -rf node_modules package-lock.json
npm install

# Clear Vite cache
rm -rf node_modules/.vite
npm run dev
```

**CORS Errors**
- Check `ALLOWED_ORIGINS` in backend configuration
- Verify frontend URL matches allowed origins

### Logs and Monitoring

```bash
# View all logs
docker-compose logs -f

# View specific service logs
docker-compose logs -f backend
docker-compose logs -f frontend

# Monitor resource usage
docker stats
```

### Health Checks

```bash
# Check service health
curl http://localhost:8000/health
curl http://localhost:3000

# Database connectivity
docker-compose exec backend python -c "
from app.core.database import engine
import asyncio
asyncio.run(engine.connect())
print('Database connected')
"
```

## 📞 Support

For issues and questions:
1. Check the troubleshooting section
2. Review logs for error details
3. Consult API documentation at `/docs`
4. Create an issue in the repository

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.
