version: '3.8'

services:
  # Backend API Service
  backend:
    build:
      context: ./cmo_backend
      dockerfile: Dockerfile
    container_name: cmo_backend
    restart: unless-stopped
    environment:
      - DATABASE_URL=postgresql+asyncpg://postgres:password@postgres:5432/cmo_db
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=${SECRET_KEY:-your-secret-key-change-in-production}
      - ENVIRONMENT=development
      - ALLOWED_ORIGINS=http://localhost:3000,http://localhost:5173,http://frontend:80
      - OPENAI_API_KEY=${OPENAI_API_KEY:-}
    ports:
      - "8000:8000"
    volumes:
      - ./cmo_backend:/app
      - backend_logs:/app/logs
    networks:
      - cmo_network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Frontend Service
  frontend:
    build:
      context: ./cmo_frontend
      dockerfile: Dockerfile
    container_name: cmo_frontend
    restart: unless-stopped
    ports:
      - "3000:80"
    volumes:
      - ./cmo_frontend/src:/app/src
      - ./cmo_frontend/public:/app/public
    networks:
      - cmo_network
    depends_on:
      - backend
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/"]
      interval: 30s
      timeout: 10s
      retries: 3

  # PostgreSQL Database Service
  postgres:
    image: postgres:15-alpine
    container_name: cmo_postgres
    restart: unless-stopped
    environment:
      POSTGRES_USER: ${POSTGRES_USER:-postgres}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-password}
      POSTGRES_DB: ${POSTGRES_DB:-cmo_db}
    ports:
      - "5433:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - cmo_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-postgres}"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis Cache Service
  redis:
    image: redis:7-alpine
    container_name: cmo_redis
    restart: unless-stopped
    ports:
      - "6380:6379"
    volumes:
      - redis_data:/data
    networks:
      - cmo_network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    command: redis-server --appendonly yes

# Named volumes for data persistence
volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  backend_logs:
    driver: local

# Custom network for service communication
networks:
  cmo_network:
    driver: bridge
