Of course. Here is a detailed checklist of tasks broken down from the development tickets. This format is designed for a development team to track progress and manage dependencies effectively.

### **Stage 1: Minimum Viable Product (MVP)**

#### ---

**Phase 1: Backend Foundations**

**Ticket: CMO-101 \- Initial FastAPI Project Scaffolding**

* **Dependencies:** None  
* **Tasks:**  
  * \[x\] **CMO-101-01:** Create the main project directory cmo\_backend.
  * \[x\] **CMO-101-02:** Initialize a Python virtual environment (e.g., python \-m venv venv) and create a requirements.txt file. 1
  * \[x\] **CMO-101-03:** Create the app/ directory with subdirectories: api/, core/, crud/, models/.
  * \[x\] **CMO-101-04:** Create app/main.py and instantiate the main FastAPI() app object.
  * \[x\] **CMO-101-05:** Implement configuration management in app/core/config.py using Pydantic's BaseSettings to load from environment variables.
  * \[x\] **CMO-101-06:** Create a .env.example file to document required environment variables like JWT\_SECRET\_KEY. 1

**Ticket: CMO-102 \- Setup PostgreSQL and Redis Services**

* **Dependencies:** None  
* **Tasks:**  
  * \[x\] **CMO-102-01:** Create an initial docker-compose.yml file.
  * \[x\] **CMO-102-02:** Add a postgres service using the official PostgreSQL image, configuring a user, password, and database name via environment variables.
  * \[x\] **CMO-102-03:** Add a redis service using the official Redis image.
  * \[x\] **CMO-102-04:** Configure Docker volumes for PostgreSQL to ensure data persistence.

**Ticket: CMO-103 \- Integrate Async SQLAlchemy and Alembic**

* **Dependencies:** CMO-101, CMO-102  
* **Tasks:**  
  * \[x\] **CMO-103-01:** Install sqlalchemy\[asyncio\], asyncpg, and alembic into the virtual environment. 2
  * \[x\] **CMO-103-02:** Create a database session manager in app/core/database.py to handle the create\_async\_engine and async\_sessionmaker. 2
  * \[x\] **CMO-103-03:** Implement a get\_db dependency injection function to provide an AsyncSession to API endpoints. 2
  * \[x\] **CMO-103-04:** Initialize Alembic using alembic init \-t async alembic. 4
  * \[x\] **CMO-103-05:** Configure alembic.ini with the correct database URL from settings. 5
  * \[x\] **CMO-103-06:** Configure alembic/env.py to use the SQLModel metadata target for autogeneration. 4

**Ticket: CMO-104 \- Define Core Data Models**

* **Dependencies:** CMO-103  
* **Tasks:**  
  * \[x\] **CMO-104-01:** Install sqlmodel.
  * \[x\] **CMO-104-02:** Create app/models/user.py and define the User model with fields for email, hashed\_password, role, etc.
  * \[x\] **CMO-104-03:** Create app/models/chat.py and define the ChatThread and ChatMessage models with appropriate relationships.
  * \[x\] **CMO-104-04:** Create Pydantic/SQLModel schemas for API operations (e.g., UserCreate, UserPublic, Token). 7
  * \[x\] **CMO-104-05:** Run alembic revision \--autogenerate \-m "Create initial tables" and alembic upgrade head to apply the models to the database. 5

**Ticket: CMO-105 \- Implement User Registration Endpoint**

* **Dependencies:** CMO-104  
* **Tasks:**  
  * \[x\] **CMO-105-01:** Install passlib\[bcrypt\].
  * \[x\] **CMO-105-02:** Create app/crud/user.py with a function to create a new user and hash their password.
  * \[x\] **CMO-105-03:** Create the POST /auth/register endpoint in an api module.
  * \[x\] **CMO-105-04:** Implement logic to check if a user with the given email already exists.

**Ticket: CMO-106 \- Implement JWT Authentication Flow**

* **Dependencies:** CMO-104  
* **Tasks:**  
  * \[x\] **CMO-106-01:** Install python-jose. 8
  * \[x\] **CMO-106-02:** Create app/core/security.py to manage JWT creation and decoding.
  * \[x\] **CMO-106-03:** Implement functions to create short-lived access\_token and long-lived refresh\_token. 9
  * \[x\] **CMO-106-04:** Create the POST /auth/token endpoint using OAuth2PasswordRequestForm.
  * \[x\] **CMO-106-05:** Implement the POST /auth/refresh endpoint that requires a valid refresh token in the Authorization header. 9
  * \[x\] **CMO-106-06:** Create a dependency get\_current\_user that validates the access token and returns the user model.

**Ticket: CMO-107 \- Implement JWT Revocation with Redis**

* **Dependencies:** CMO-106  
* **Tasks:**  
  * \[x\] **CMO-107-01:** Create the POST /auth/logout endpoint.
  * \[x\] **CMO-107-02:** On logout, add the jti (JWT ID) of the access and refresh tokens to a Redis blocklist set. 10
  * \[x\] **CMO-107-03:** Set an expiry on the Redis keys equal to the token's remaining lifetime.
  * \[x\] **CMO-107-04:** Update the get\_current\_user dependency to check the Redis blocklist before validating the token.

**Ticket: CMO-108 \- Create User Account Management Endpoints**

* **Dependencies:** CMO-106  
* **Tasks:**  
  * \[x\] **CMO-108-01:** Implement the GET /users/me endpoint, protected by get\_current\_user.
  * \[x\] **CMO-108-02:** Implement the PATCH /users/me endpoint to allow users to update their preferred\_language.

**Ticket: CMO-109 \- Create Chat History API Endpoints**

* **Dependencies:** CMO-104, CMO-106  
* **Tasks:**  
  * \[x\] **CMO-109-01:** Implement the GET /chat/history endpoint to retrieve all ChatThreads for the current user.
  * \[x\] **CMO-109-02:** Implement the GET /chat/history/{thread\_id} endpoint to retrieve a specific thread with all its ChatMessages.

#### ---

**Phase 2: Frontend Foundations**

**Ticket: CMO-201 \- \[Frontend\] Initial React Project Scaffolding**

* **Dependencies:** None
* **Tasks:**
  * \[x\] **CMO-201-01:** Run npm create vite@latest cmo\_frontend \-- \--template react-ts. 12
  * \[x\] **CMO-201-02:** cd cmo\_frontend and run npm install.
  * \[x\] **CMO-201-03:** Establish the project folder structure: src/api, src/app, src/components, src/features, src/hooks, src/pages, etc.
  * \[x\] **CMO-201-04:** Configure vite.config.ts with path aliases (e.g., @/components). 14

**Ticket: CMO-202 \- \[Frontend\] Setup Redux Toolkit for State Management**

* **Dependencies:** CMO-201
* **Tasks:**
  * \[x\] **CMO-202-01:** Install @reduxjs/toolkit and react-redux. 15
  * \[x\] **CMO-202-02:** Create the Redux store in src/app/store.ts using configureStore. 15
  * \[x\] **CMO-202-03:** Wrap the root \<App /\> component with the \<Provider\> in src/main.tsx.

**Ticket: CMO-203 \- \[Frontend\] Create authSlice for Authentication State**

* **Dependencies:** CMO-202
* **Tasks:**
  * \[x\] **CMO-203-01:** Create src/features/auth/authSlice.ts using createSlice.
  * \[x\] **CMO-203-02:** Define the initial state for user, isAuthenticated, accessToken, and refreshToken.
  * \[x\] **CMO-203-03:** Create reducers for loginSuccess, logoutSuccess, and tokensRefreshed.

**Ticket: CMO-204 \- \[Frontend\] Configure Centralized API Client (Axios)**

* **Dependencies:** CMO-203
* **Tasks:**
  * \[x\] **CMO-204-01:** Install axios.
  * \[x\] **CMO-204-02:** Create an Axios instance in src/api/client.ts with the backend base URL.
  * \[x\] **CMO-204-03:** Implement a request interceptor to attach the accessToken to the Authorization header.
  * \[x\] **CMO-204-04:** Implement a response interceptor to handle 401 errors by calling the /auth/refresh endpoint and retrying the original request.

**Ticket: CMO-205 \- \[Frontend\] Implement Application Routing**

* **Dependencies:** CMO-201
* **Tasks:**
  * \[x\] **CMO-205-01:** Install react-router-dom.
  * \[x\] **CMO-205-02:** Create a router configuration in src/app/router.tsx using createBrowserRouter.
  * \[x\] **CMO-205-03:** Set up basic routes for public pages like /login and /register.

**Ticket: CMO-206 \- \[Frontend\] Implement Protected Route Layouts**

* **Dependencies:** CMO-203, CMO-205
* **Tasks:**
  * \[x\] **CMO-206-01:** Create a ProtectedRouteLayout.tsx component that checks for authentication status. 16
  * \[x\] **CMO-206-02:** If the user is not authenticated, the layout should render \<Navigate to="/login" /\>. 17
  * \[x\] **CMO-206-03:** If the user is authenticated, the layout should render the \<Outlet /\> component. 17
  * \[x\] **CMO-206-04:** Update the router configuration to nest protected routes like /chat and /account as children of the ProtectedRouteLayout. 16

**Ticket: CMO-207 \- \[Frontend\] Build User Registration Page**

* **Dependencies:** CMO-204, CMO-205
* **Tasks:**
  * \[x\] **CMO-207-01:** Create the RegistrationPage.tsx component with a form for email and password.
  * \[x\] **CMO-207-02:** On form submission, call the POST /auth/register endpoint.

**Ticket: CMO-208 \- \[Frontend\] Build User Login Page**

* **Dependencies:** CMO-204, CMO-205
* **Tasks:**
  * \[x\] **CMO-208-01:** Create the LoginPage.tsx component with a form for email and password.
  * \[x\] **CMO-208-02:** On form submission, call the POST /auth/token endpoint.
  * \[x\] **CMO-208-03:** On success, dispatch the loginSuccess action with user data and tokens.

**Ticket: CMO-209 \- \[Frontend\] Build User Account Page**

* **Dependencies:** CMO-206, CMO-204
* **Tasks:**
  * \[x\] **CMO-209-01:** Create the AccountPage.tsx component.
  * \[x\] **CMO-209-02:** Fetch and display user profile information from the GET /users/me endpoint.
  * \[x\] **CMO-209-03:** Implement UI for updating user preferences (e.g., language).

#### ---

**Phase 3: Core Chat Functionality**

**Ticket: CMO-301 \- Implement Streaming Chat Endpoint**

* **Dependencies:** CMO-106
* **Tasks:**
  * \[x\] **CMO-301-01:** Create the POST /chat endpoint that accepts a user's message.
  * \[x\] **CMO-301-02:** Connect to the OpenAI Assistant's streaming API.
  * \[x\] **CMO-301-03:** Implement an async generator that yields response chunks in Server-Sent Events (SSE) format.
  * \[x\] **CMO-301-04:** Return a StreamingResponse from the endpoint.

**Ticket: CMO-302 \- \[Frontend\] Create chatSlice for Chat State**

* **Dependencies:** CMO-203
* **Tasks:**
  * \[x\] **CMO-302-01:** Create src/features/chat/chatSlice.ts.
  * \[x\] **CMO-302-02:** Define state for messages array and status (e.g., 'idle', 'loading', 'succeeded', 'failed'). 19
  * \[x\] **CMO-302-03:** Create a sendMessage async thunk using createAsyncThunk. 19
  * \[x\] **CMO-302-04:** Create synchronous reducers like appendAiMessageChunk and messageStreamComplete.

**Ticket: CMO-303 \- \[Frontend\] Build Core Chat UI**

* **Dependencies:** CMO-302
* **Tasks:**
  * \[x\] **CMO-303-01:** Create the ChatPage.tsx component.
  * \[x\] **CMO-303-02:** Build the message display area that maps over the messages array from the Redux store.
  * \[x\] **CMO-303-03:** Build the text input form that dispatches the sendMessage thunk on submit.
  * \[x\] **CMO-303-04:** Add a loading indicator that displays when chat.status is 'loading'.

**Ticket: CMO-304 \- \[Frontend\] Implement EventSource for Stream Consumption**

* **Dependencies:** CMO-301, CMO-303
* **Tasks:**
  * \[x\] **CMO-304-01:** In ChatPage.tsx, use a useEffect hook to create a new EventSource instance when a message is sent.
  * \[x\] **CMO-304-02:** Add an onmessage listener to the EventSource.
  * \[x\] **CMO-304-03:** Inside the listener, dispatch appendAiMessageChunk with the received data.
  * \[x\] **CMO-304-04:** Add onerror and onopen listeners to handle stream completion and connection state.

**Ticket: CMO-305 \- Persist Chat Messages to Database**

* **Dependencies:** CMO-301, CMO-104
* **Tasks:**
  * \[x\] **CMO-305-01:** Modify the /chat endpoint to first create a new ChatThread in the database.
  * \[x\] **CMO-305-02:** Save the user's message as a ChatMessage linked to the thread.
  * \[x\] **CMO-305-03:** After the AI stream is complete, save the full AI response as a ChatMessage.

**Ticket: CMO-306 \- \[Frontend\] Build Chat History Page**

* **Dependencies:** CMO-109, CMO-206
* **Tasks:**
  * \[x\] **CMO-306-01:** Create a ChatHistoryPage.tsx component.
  * \[x\] **CMO-306-02:** Fetch and display a list of past chat threads from GET /chat/history.
  * \[x\] **CMO-306-03:** Allow users to click a thread to navigate to a detailed view, fetching data from GET /chat/history/{thread\_id}.

#### **Phase 4: Internationalization (i18n)**

**Ticket: CMO-401 \- \[Frontend\] Configure react-i18next**

* **Dependencies:** CMO-201
* **Tasks:**
  * \[x\] **CMO-401-01:** Install react-i18next, i18next, i18next-http-backend, and i18next-browser-languagedetector.
  * \[x\] **CMO-401-02:** Create the directory structure for translation files: public/locales/en/translation.json and public/locales/es/translation.json.
  * \[x\] **CMO-401-03:** Create the src/i18n.ts configuration file, importing the necessary libraries and setting up the i18next instance.
  * \[x\] **CMO-401-04:** Wrap the root App component with the \<I18nextProvider\> in main.tsx.

**Ticket: CMO-402 \- \[Frontend\] Internationalize All UI Components**

* **Dependencies:** CMO-401
* **Tasks:**
  * \[x\] **CMO-402-01:** Go through all pages and components (LoginPage, ChatPage, AccountPage, etc.) and replace hardcoded English text with the t() function from the useTranslation hook.
  * \[x\] **CMO-402-02:** Populate the translation.json files with the corresponding keys and translations.
  * \[x\] **CMO-402-03:** Use the \<Trans\> component where translations include JSX elements, such as links.

**Ticket: CMO-403 \- \[Frontend\] Implement Language Switcher Component**

* **Dependencies:** CMO-401
* **Tasks:**
  * \[x\] **CMO-403-01:** Create a LanguageSwitcher.tsx component (e.g., a dropdown menu).
  * \[x\] **CMO-403-02:** Use the i18n.changeLanguage() function in the component's event handler to switch the language.
  * \[x\] **CMO-403-03:** Add the LanguageSwitcher component to a shared layout, like the main navigation bar.

**Ticket: CMO-404 \- \[Frontend\] Persist User Language Preference**

* **Dependencies:** CMO-403, CMO-108, CMO-209
* **Tasks:**
  * \[x\] **CMO-404-01:** When the language is changed via the switcher, dispatch an API call to PATCH /users/me to save the new language preference on the user's profile.
  * \[x\] **CMO-404-02:** On initial application load, after fetching the user profile, use the saved language preference to initialize the i18next instance.

#### ---

**Phase 5: Containerization and CI/CD**

**Ticket: CMO-501 \- Create Backend Dockerfile**

* **Dependencies:** CMO-101
* **Tasks:**
  * \[x\] **CMO-501-01:** Create a Dockerfile in the backend's root directory.
  * \[x\] **CMO-501-02:** Implement a multi-stage build: a builder stage to install dependencies using pip, and a final, smaller production stage.
  * \[x\] **CMO-501-03:** Use gunicorn with uvicorn.workers.UvicornWorker as the CMD for running the application.

**Ticket: CMO-502 \- Create Frontend Dockerfile**

* **Dependencies:** CMO-201
* **Tasks:**
  * \[x\] **CMO-502-01:** Create a Dockerfile in the frontend's root directory.
  * \[x\] **CMO-502-02:** Implement a multi-stage build: a builder stage to install Node.js dependencies and run npm run build.
  * \[x\] **CMO-502-03:** Use a lightweight nginx image for the final stage and copy the built static files into it.

**Ticket: CMO-503 \- Create docker-compose.yml for Local Dev**

* **Dependencies:** CMO-501, CMO-502, CMO-102
* **Tasks:**
  * \[x\] **CMO-503-01:** Create a docker-compose.yml file at the project's root level.
  * \[x\] **CMO-503-02:** Define the services: backend, frontend, db (Postgres), and cache (Redis).
  * \[x\] **CMO-503-03:** Configure networking so the services can communicate.
  * \[x\] **CMO-503-04:** Set up volumes for persistent database storage and for live-reloading of code during development.

**Ticket: CMO-504 \- Configure Nginx Reverse Proxy**

* **Dependencies:** CMO-502
* **Tasks:**
  * \[x\] **CMO-504-01:** Create an nginx/nginx.conf file.
  * \[x\] **CMO-504-02:** Configure Nginx to serve the React application's static files from the /usr/share/nginx/html directory.
  * \[x\] **CMO-504-03:** Add a location /api block to proxy all API requests to the backend service (e.g., proxy\_pass http://backend:8000;).
  * \[x\] **CMO-504-04:** Ensure the frontend Dockerfile copies this nginx.conf file into the Nginx image.

**Ticket: CMO-505 \- Create Backend CI Workflow**

* **Dependencies:** CMO-101
* **Tasks:**
  * \[x\] **CMO-505-01:** Create .github/workflows/ci.yml.
  * \[x\] **CMO-505-02:** Add a job named backend-ci that runs on every push.
  * \[x\] **CMO-505-03:** Add steps to check out code, set up Python, install dependencies, and run linters (flake8).
  * \[x\] **CMO-505-04:** Add a step to run unit tests using pytest.

**Ticket: CMO-506 \- Create Frontend CI Workflow**

* **Dependencies:** CMO-201
* **Tasks:**
  * \[x\] **CMO-506-01:** Add a job named frontend-ci to the ci.yml workflow.
  * \[x\] **CMO-506-02:** Add steps to check out code, set up Node.js, install dependencies (npm install), and run linters (npm run lint).
  * \[x\] **CMO-506-03:** Add a step to run unit tests (npm test).

**Ticket: CMO-507 \- Create CD Workflow for Building Images**

* **Dependencies:** CMO-503
* **Tasks:**
  * \[x\] **CMO-507-01:** Create a new workflow file .github/workflows/cd.yml that is triggered on pushes to the main branch.
  * \[x\] **CMO-507-02:** Add steps to log in to a container registry (e.g., Docker Hub, GHCR).
  * \[x\] **CMO-507-03:** Add steps to build and push the backend and frontend Docker images, tagging them appropriately (e.g., with the Git SHA).

**Ticket: CMO-508 \- Add Deployment Step to CD Workflow**

* **Dependencies:** CMO-507
* **Tasks:**
  * \[x\] **CMO-508-01:** Add a final job to the cd.yml workflow named deploy.
  * \[x\] **CMO-508-02:** Use a community action (e.g., appleboy/ssh-action) to connect to the production server via SSH, using secrets for credentials.
  * \[x\] **CMO-508-03:** Add commands in the SSH step to navigate to the project directory, pull the latest Docker images, and restart the services using docker-compose up \-d \--force-recreate.

### **Stage 2: Expansion with Advanced Data Sources**

#### ---

**Phase 6: The Cognitive Core: Designing the Orchestration Layer**

**Ticket: CMO-601 \- Design Planner-Executor Orchestration Model**

* **Dependencies:** CMO-301  
* **Tasks:**  
  * \[ \] **CMO-601-01:** Create a new module app/core/orchestrator/.  
  * \[ \] **CMO-601-02:** Implement the **Planner** module, which takes a user query and uses an LLM to generate a structured JSON plan of which "tools" to use. 1  
  * \[ \] **CMO-601-03:** Implement the **Executor** module, which receives the plan from the Planner and is responsible for iterating through the steps.  
  * \[ \] **CMO-601-04:** Refactor the POST /chat endpoint to use the new Planner-Executor model as its core logic.

**Ticket: CMO-602 \- Implement DataSourceStrategy Pattern**

* **Dependencies:** CMO-601  
* **Tasks:**  
  * \[ \] **CMO-602-01:** Create app/core/orchestrator/strategies.py.  
  * \[ \] **CMO-602-02:** Define an abstract base class DataSourceStrategy with an async def execute(self, query: str) method. 2  
  * \[ \] **CMO-602-03:** In the Executor module, create a tool registry (e.g., a dictionary) to map tool names (e.g., "live\_web\_search") to instances of the concrete strategy classes.

**Ticket: CMO-603 \- Implement Synthesizer Module**

* **Dependencies:** CMO-601  
* **Tasks:**  
  * \[ \] **CMO-603-01:** Create a Synthesizer module within the orchestrator.  
  * \[ \] **CMO-603-02:** Implement logic to collect and format all the evidence gathered by the Executor.  
  * \[ \] **CMO-603-03:** Implement logic to send the collated evidence and the original query to a high-quality LLM to generate the final, synthesized answer.  
  * \[ \] **CMO-603-04:** Integrate the Synthesizer as the final step in the orchestration flow before streaming the response to the user.

#### ---

**Phase 7: Integrating Multi-Modal Data Sources**

**Ticket: CMO-701 \- Integrate Qdrant Vector Database**

* **Dependencies:** CMO-503  
* **Tasks:**  
  * \[ \] **CMO-701-01:** Add the official Qdrant Docker image as a new service in the docker-compose.yml file.  
  * \[ \] **CMO-701-02:** Create a client module in the backend (app/core/vector\_db.py) to handle connections and interactions with the Qdrant service.

**Ticket: CMO-702 \- Develop Content-Aware Chunking Pipeline**

* **Dependencies:** CMO-701  
* **Tasks:**  
  * \[ \] **CMO-702-01:** Research and implement a strategy for parsing scientific documents (e.g., PDFs) to identify logical structures like paragraphs, sections, and tables. 3  
  * \[ \] **CMO-702-02:** Implement a chunking algorithm that splits documents along these logical boundaries. 5  
  * \[ \] **CMO-702-03:** Implement a background task (e.g., using FastAPI's BackgroundTasks) that handles the full ingestion pipeline: document parsing, chunking, embedding, and storing in Qdrant.

**Ticket: CMO-703 \- Implement RagStrategy**

* **Dependencies:** CMO-602, CMO-701  
* **Tasks:**  
  * \[ \] **CMO-703-01:** Create a RagStrategy class in strategies.py that inherits from DataSourceStrategy.  
  * \[ \] **CMO-703-02:** Implement the execute method to take a query, generate its vector embedding, and search for the top-k similar chunks in Qdrant.  
  * \[ \] **CMO-703-03:** Register the RagStrategy instance in the Executor's tool registry.

**Ticket: CMO-704 \- Integrate Neo4j Graph Database**

* **Dependencies:** CMO-503  
* **Tasks:**  
  * \[ \] **CMO-704-01:** Add the official Neo4j Docker image as a new service in the docker-compose.yml file.  
  * \[ \] **CMO-704-02:** Create a client module in the backend (app/core/graph\_db.py) to handle connections and Cypher queries with the Neo4j service.

**Ticket: CMO-705 \- Design and Implement Neo4j Metabolomics Schema**

* **Dependencies:** CMO-704  
* **Tasks:**  
  * \[ \] **CMO-705-01:** Define the node labels for the knowledge graph (e.g., Metabolite, Protein, Pathway, Disease). 6  
  * \[ \] **CMO-705-02:** Define the relationship types that connect the nodes (e.g., CATALYZES, PART\_OF, ASSOCIATED\_WITH). 6  
  * \[ \] **CMO-705-03:** Document the final schema in the project's README or wiki.

**Ticket: CMO-706 \- Develop Neo4j Ingestion Pipeline**

* **Dependencies:** CMO-705  
* **Tasks:**  
  * \[ \] **CMO-706-01:** Write a parser to extract data from public metabolomics databases (e.g., KEGG, HMDB). 8  
  * \[ \] **CMO-706-02:** Implement a script that uses the parser's output to populate the Neo4j database with nodes and relationships according to the defined schema. 11

**Ticket: CMO-707 \- Implement GraphStrategy**

* **Dependencies:** CMO-602, CMO-704  
* **Tasks:**  
  * \[ \] **CMO-707-01:** Create a GraphStrategy class in strategies.py.  
  * \[ \] **CMO-707-02:** Implement the execute method to convert a natural language query into a Cypher query (initially using templates, potentially using an LLM later). 13  
  * \[ \] **CMO-707-03:** The execute method should run the Cypher query against Neo4j and return the formatted results. 15  
  * \[ \] **CMO-707-04:** Register the GraphStrategy instance in the Executor's tool registry.

**Ticket: CMO-708 \- Implement WebSearchStrategy**

* **Dependencies:** CMO-602  
* **Tasks:**  
  * \[ \] **CMO-708-01:** Create a WebSearchStrategy class in strategies.py.  
  * \[ \] **CMO-708-02:** Implement the execute method to call the Perplexity API with the given query. 16  
  * \[ \] **CMO-708-03:** The method should parse the API response and return the answer and sources as evidence. 18  
  * \[ \] **CMO-708-04:** Register the WebSearchStrategy instance in the Executor's tool registry.

**Ticket: CMO-709 \- Implement OpenAIAssistantStrategy**

* **Dependencies:** CMO-602  
* **Tasks:**  
  * \[ \] **CMO-709-01:** Create an OpenAIAssistantStrategy class in strategies.py.  
  * \[ \] **CMO-709-02:** Move the original OpenAI Assistant call logic from the MVP's /chat endpoint into the execute method of this new strategy.  
  * \[ \] **CMO-709-03:** Register this strategy in the Executor's tool registry, to be used as a fallback or for general knowledge questions.

#### ---

**Phase 8: UI Enhancement for Transparent Reasoning**

**Ticket: CMO-801 \- Stream Orchestrator Status Updates**

* **Dependencies:** CMO-601  
* **Tasks:**  
  * \[ \] **CMO-801-01:** Modify the /chat endpoint's async generator.  
  * \[ \] **CMO-801-02:** Before the Executor calls each tool, yield a JSON object with {"type": "status", "message": "Querying knowledge graph..."}.  
  * \[ \] **CMO-801-03:** Ensure the final answer tokens are yielded in a different format, e.g., {"type": "token", "content": "..."}.

**Ticket: CMO-802 \- \[Frontend\] Enhance EventSource Handler for Status Events**

* **Dependencies:** CMO-304  
* **Tasks:**  
  * \[ \] **CMO-802-01:** Add a currentStatus: string | null field to the chatSlice state.  
  * \[ \] **CMO-802-02:** Create a new reducer, setChatStatus, to update this field.  
  * \[ \] **CMO-802-03:** In the EventSource's onmessage handler, parse the incoming JSON and check the type field.  
  * \[ \] **CMO-802-04:** Dispatch setChatStatus for status events and appendAiMessageChunk for token events.

**Ticket: CMO-803 \- \[Frontend\] Create StatusDisplay UI Component**

* **Dependencies:** CMO-802  
* **Tasks:**  
  * \[ \] **CMO-803-01:** Create a new React component, StatusDisplay.tsx.  
  * \[ \] **CMO-803-02:** The component should read the currentStatus from the Redux store.  
  * \[ \] **CMO-803-03:** In ChatPage.tsx, conditionally render the StatusDisplay component whenever the overall chat status is 'loading'.

#### ---

**Phase 9: Admin Panel**

**Ticket: CMO-901 \- Create Secure Admin API Endpoints**

* **Dependencies:** CMO-108  
* **Tasks:**  
  * \[ \] **CMO-901-01:** Create a new dependency get\_current\_admin\_user that checks if user.role is 'admin'.  
  * \[ \] **CMO-901-02:** Create a new API router file under app/api/admin/.  
  * \[ \] **CMO-901-03:** Protect all endpoints in this new router with Depends(get\_current\_admin\_user).

**Ticket: CMO-902 \- \[Frontend\] Build Admin UI for User Management**

* **Dependencies:** CMO-901, CMO-206  
* **Tasks:**  
  * \[ \] **CMO-902-01:** Create an AdminPage.tsx protected by the AdminRouteLayout.  
  * \[ \] **CMO-902-02:** Implement a component to fetch from GET /admin/users and display users in a table.  
  * \[ \] **CMO-902-03:** Add UI controls (buttons, modals) to call the PATCH and DELETE user endpoints.

**Ticket: CMO-903 \- Implement API for RAG Document Management**

* **Dependencies:** CMO-901, CMO-702  
* **Tasks:**  
  * \[ \] **CMO-903-01:** Create a POST /admin/rag/documents endpoint that accepts file uploads and triggers the ingestion background task.  
  * \[ \] **CMO-903-02:** Create a GET /admin/rag/documents endpoint to list documents and their processing status.  
  * \[ \] **CMO-903-03:** Create a DELETE /admin/rag/documents/{doc\_id} endpoint.

**Ticket: CMO-904 \- \[Frontend\] Build Admin UI for RAG Document Management**

* **Dependencies:** CMO-903, CMO-206  
* **Tasks:**  
  * \[ \] **CMO-904-01:** Create a component within the admin area for RAG management.  
  * \[ \] **CMO-904-02:** Implement a file upload form that calls the POST endpoint.  
  * \[ \] **CMO-904-03:** Display the list of documents and their status from the GET endpoint.

**Ticket: CMO-905 \- Implement API for KG Management**

* **Dependencies:** CMO-901, CMO-706  
* **Tasks:**  
  * \[ \] **CMO-905-01:** Create a POST /admin/kg/manage endpoint.  
  * \[ \] **CMO-905-02:** Implement logic for this endpoint to trigger backend maintenance scripts, like the Neo4j ingestion pipeline.

**Ticket: CMO-906 \- \[Frontend\] Build Admin UI for KG Management**

* **Dependencies:** CMO-905, CMO-206  
* **Tasks:**  
  * \[ \] **CMO-906-01:** Create a component within the admin area for KG management.  
  * \[ \] **CMO-906-02:** Add buttons (e.g., "Refresh Knowledge Graph") that call the POST endpoint.

**Ticket: CMO-907 \- Implement API for Orchestrator Configuration**

* **Dependencies:** CMO-901, CMO-601  
* **Tasks:**  
  * \[ \] **CMO-907-01:** Create a GET /admin/orchestrator/config endpoint to return the current configuration.  
  * \[ \] **CMO-907-02:** Create a PUT /admin/orchestrator/config endpoint to update the configuration.

**Ticket: CMO-908 \- \[Frontend\] Build Admin UI for Orchestrator Configuration**

* **Dependencies:** CMO-907, CMO-206  
* **Tasks:**  
  * \[ \] **CMO-908-01:** Create a component within the admin area for orchestrator settings.  
  * \[ \] **CMO-908-02:** Build a form with inputs for each parameter (API keys, model names, etc.).  
  * \[ \] **CMO-908-03:** Fetch initial state with the GET endpoint and save changes with the PUT endpoint.

#### ---

**Phase 10: Comprehensive System Testing and Final Deployment**

**Ticket: CMO-1001 \- \[QA\] Implement E2E Testing with Cypress**

* **Dependencies:** All MVP tickets  
* **Tasks:**  
  * \[ \] **CMO-1001-01:** Install and configure Cypress in the frontend project.  
  * \[ \] **CMO-1001-02:** Write a test script for the full user registration, login, and logout flow.  
  * \[ \] **CMO-1001-03:** Write a test script for the MVP chat functionality.

**Ticket: CMO-1002 \- \[QA\] Expand E2E Tests for Advanced Features**

* **Dependencies:** All Stage 2 tickets  
* **Tasks:**  
  * \[ \] **CMO-1002-01:** Write a Cypress test for a query that triggers multiple data sources and verifies the status updates.  
  * \[ \] **CMO-1002-02:** Write a Cypress test for the admin login flow and verify access to the admin panel.  
  * \[ \] **CMO-1002-03:** Write a Cypress test for uploading a document via the admin panel and verifying its status.

**Ticket: CMO-1003 \- Update Production Docker Compose**

* **Dependencies:** CMO-701, CMO-704  
* **Tasks:**  
  * \[ \] **CMO-1003-01:** Create a docker-compose.prod.yml file.  
  * \[ \] **CMO-1003-02:** Add the qdrant and neo4j services to this file, ensuring they are configured for production.

**Ticket: CMO-1004 \- Update CD Pipeline for Full Stack**

* **Dependencies:** CMO-508, CMO-1003  
* **Tasks:**  
  * \[ \] **CMO-1004-01:** Add new secrets to GitHub Actions for Neo4j credentials and the Perplexity API key. 19  
  * \[ \] **CMO-1004-02:** Modify the cd.yml workflow to use the docker-compose.prod.yml file in the final deployment step. 21