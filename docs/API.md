# Clinical Metabolomics Oracle - API Documentation

Complete API reference for the Clinical Metabolomics Oracle backend services.

## 📋 Overview

The CMO API is built with FastAPI and provides RESTful endpoints for:
- User authentication and management
- AI-powered chat functionality
- Clinical metabolomics data analysis
- Real-time streaming responses

**Base URL**: `http://localhost:8000`  
**API Documentation**: `http://localhost:8000/docs`  
**Alternative Docs**: `http://localhost:8000/redoc`

## 🔐 Authentication

The API uses JWT (JSON Web Tokens) for authentication with access and refresh token pairs.

### Token Lifecycle
- **Access Token**: 15 minutes (for API requests)
- **Refresh Token**: 7 days (for token renewal)
- **Token Type**: Bearer

### Authentication Flow
1. Register user or login with credentials
2. Receive access and refresh tokens
3. Include access token in Authorization header
4. Refresh tokens before expiration

## 📚 API Endpoints

### Authentication Endpoints

#### Register User
Creates a new user account.

```http
POST /auth/register
Content-Type: application/json
```

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "secure_password123",
  "preferred_language": "en"
}
```

**Response (201 Created):**
```json
{
  "id": 1,
  "email": "<EMAIL>",
  "preferred_language": "en",
  "role": "user",
  "is_active": true,
  "created_at": "2024-01-01T00:00:00Z"
}
```

**Error Responses:**
- `400 Bad Request`: User already exists or invalid data
- `500 Internal Server Error`: Server error

#### Login
Authenticates user and returns JWT tokens.

```http
POST /auth/token
Content-Type: application/x-www-form-urlencoded
```

**Request Body:**
```
username=<EMAIL>&password=secure_password123
```

**Response (200 OK):**
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer",
  "expires_in": 900
}
```

**Error Responses:**
- `401 Unauthorized`: Invalid credentials
- `400 Bad Request`: Inactive user account

#### Refresh Token
Generates new token pair using refresh token.

```http
POST /auth/refresh
Content-Type: application/json
```

**Request Body:**
```json
{
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

**Response (200 OK):**
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer",
  "expires_in": 900
}
```

#### Logout
Invalidates refresh token by adding to blocklist.

```http
POST /auth/logout
Content-Type: application/json
Authorization: Bearer {access_token}
```

**Request Body:**
```json
{
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

**Response (200 OK):**
```json
{
  "message": "Successfully logged out"
}
```

### User Management Endpoints

#### Get Current User
Retrieves current user profile.

```http
GET /users/me
Authorization: Bearer {access_token}
```

**Response (200 OK):**
```json
{
  "id": 1,
  "email": "<EMAIL>",
  "preferred_language": "en",
  "role": "user",
  "is_active": true,
  "created_at": "2024-01-01T00:00:00Z"
}
```

#### Update User Profile
Updates current user information.

```http
PUT /users/me
Content-Type: application/json
Authorization: Bearer {access_token}
```

**Request Body:**
```json
{
  "preferred_language": "es"
}
```

**Response (200 OK):**
```json
{
  "id": 1,
  "email": "<EMAIL>",
  "preferred_language": "es",
  "role": "user",
  "is_active": true,
  "created_at": "2024-01-01T00:00:00Z"
}
```

### Chat Endpoints

#### Start/Continue Chat
Initiates or continues a chat conversation with AI assistant.

```http
POST /chat/
Content-Type: application/json
Authorization: Bearer {access_token}
```

**Request Body:**
```json
{
  "message": "What is metabolomics and how is it used in clinical research?",
  "thread_id": null
}
```

**Response**: Server-Sent Events (SSE) stream

**SSE Event Types:**
```javascript
// Token streaming
data: {"type": "token", "content": "Metabolomics"}

// Completion
data: {"type": "complete"}

// Error
data: {"type": "error", "message": "Error description"}
```

**JavaScript Example:**
```javascript
const eventSource = new EventSource('/chat/', {
  headers: {
    'Authorization': 'Bearer ' + accessToken
  }
});

eventSource.onmessage = function(event) {
  const data = JSON.parse(event.data);
  
  switch(data.type) {
    case 'token':
      // Append token to chat display
      appendToChat(data.content);
      break;
    case 'complete':
      // Chat response complete
      onChatComplete();
      break;
    case 'error':
      // Handle error
      showError(data.message);
      break;
  }
};
```

#### Get Chat History
Retrieves user's chat threads.

```http
GET /chat/history?skip=0&limit=10
Authorization: Bearer {access_token}
```

**Query Parameters:**
- `skip` (integer): Number of threads to skip (pagination)
- `limit` (integer): Maximum threads to return (1-100)

**Response (200 OK):**
```json
[
  {
    "id": 1,
    "title": "Chat - What is metabolomics...",
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:05:00Z"
  },
  {
    "id": 2,
    "title": "Chat - Clinical applications...",
    "created_at": "2024-01-01T01:00:00Z",
    "updated_at": "2024-01-01T01:10:00Z"
  }
]
```

#### Get Chat Thread with Messages
Retrieves specific chat thread with all messages.

```http
GET /chat/history/{thread_id}
Authorization: Bearer {access_token}
```

**Response (200 OK):**
```json
{
  "id": 1,
  "title": "Chat - What is metabolomics...",
  "created_at": "2024-01-01T00:00:00Z",
  "updated_at": "2024-01-01T00:05:00Z",
  "messages": [
    {
      "id": 1,
      "content": "What is metabolomics?",
      "sender": "user",
      "timestamp": "2024-01-01T00:00:00Z"
    },
    {
      "id": 2,
      "content": "Metabolomics is the scientific study of chemical processes...",
      "sender": "ai",
      "timestamp": "2024-01-01T00:00:30Z"
    }
  ]
}
```

**Error Responses:**
- `404 Not Found`: Thread not found or not owned by user

### Health Check Endpoints

#### Application Health
Basic health check endpoint.

```http
GET /health
```

**Response (200 OK):**
```json
{
  "status": "healthy"
}
```

#### Root Endpoint
Application information.

```http
GET /
```

**Response (200 OK):**
```json
{
  "message": "Clinical Metabolomics Oracle API",
  "version": "1.0.0",
  "status": "operational"
}
```

## 🔧 Request/Response Formats

### Content Types
- **Request**: `application/json` (except OAuth2 login)
- **Response**: `application/json`
- **Streaming**: `text/event-stream` (SSE)

### Date Formats
All timestamps use ISO 8601 format: `YYYY-MM-DDTHH:MM:SSZ`

### Error Response Format
```json
{
  "detail": "Error description",
  "type": "error_type",
  "code": "ERROR_CODE"
}
```

## 🚨 Error Codes

### HTTP Status Codes
- `200 OK`: Successful request
- `201 Created`: Resource created successfully
- `400 Bad Request`: Invalid request data
- `401 Unauthorized`: Authentication required
- `403 Forbidden`: Insufficient permissions
- `404 Not Found`: Resource not found
- `422 Unprocessable Entity`: Validation error
- `500 Internal Server Error`: Server error

### Custom Error Types
- `VALIDATION_ERROR`: Request validation failed
- `AUTHENTICATION_ERROR`: Invalid credentials
- `AUTHORIZATION_ERROR`: Insufficient permissions
- `RESOURCE_NOT_FOUND`: Requested resource not found
- `OPENAI_ERROR`: OpenAI API error
- `DATABASE_ERROR`: Database operation failed

## 🔒 Security Considerations

### Authentication Headers
```http
Authorization: Bearer {access_token}
```

### CORS Configuration
Allowed origins configured via `ALLOWED_ORIGINS` environment variable.

### Rate Limiting
- Authentication endpoints: 5 requests/minute
- Chat endpoints: 10 requests/minute
- Other endpoints: 100 requests/minute

### Input Validation
- Email format validation
- Password strength requirements (8+ characters)
- Message length limits (10,000 characters)
- SQL injection prevention
- XSS protection

## 📊 Usage Examples

### Complete Authentication Flow

```python
import requests

# 1. Register user
response = requests.post('http://localhost:8000/auth/register', json={
    'email': '<EMAIL>',
    'password': 'secure_password123'
})
user = response.json()

# 2. Login
response = requests.post('http://localhost:8000/auth/token', data={
    'username': '<EMAIL>',
    'password': 'secure_password123'
})
tokens = response.json()

# 3. Use access token
headers = {'Authorization': f"Bearer {tokens['access_token']}"}
response = requests.get('http://localhost:8000/users/me', headers=headers)
user_profile = response.json()

# 4. Refresh token when needed
response = requests.post('http://localhost:8000/auth/refresh', json={
    'refresh_token': tokens['refresh_token']
})
new_tokens = response.json()
```

### Chat Integration

```javascript
// Start new chat
async function startChat(message) {
  const response = await fetch('/chat/', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${accessToken}`
    },
    body: JSON.stringify({
      message: message,
      thread_id: null
    })
  });

  const reader = response.body.getReader();
  const decoder = new TextDecoder();

  while (true) {
    const { value, done } = await reader.read();
    if (done) break;

    const chunk = decoder.decode(value);
    const lines = chunk.split('\n');

    for (const line of lines) {
      if (line.startsWith('data: ')) {
        const data = JSON.parse(line.slice(6));
        handleChatEvent(data);
      }
    }
  }
}
```

## 🔧 Development Tools

### API Testing
```bash
# Using curl
curl -X POST http://localhost:8000/auth/register \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"test123"}'

# Using httpie
http POST localhost:8000/auth/register email=<EMAIL> password=test123
```

### OpenAPI Schema
Access the OpenAPI schema at: `http://localhost:8000/openapi.json`

### Interactive Documentation
- **Swagger UI**: `http://localhost:8000/docs`
- **ReDoc**: `http://localhost:8000/redoc`

## 🔄 WebSocket Support (Future)

The API is designed to support WebSocket connections for real-time features:
- Live chat updates
- Real-time notifications
- Collaborative features

## 📈 Monitoring and Analytics

### Health Monitoring
```http
GET /health
GET /metrics  # Prometheus metrics (if enabled)
```

### Performance Metrics
- Response times tracked per endpoint
- Error rates monitored
- Token usage analytics
- Database query performance

## 📞 Support

For API-related issues:
1. Check the interactive documentation at `/docs`
2. Verify authentication tokens are valid
3. Review error responses for details
4. Check server logs for backend errors
5. Consult the troubleshooting guide

## 📚 Additional Resources

- [Installation Guide](./INSTALLATION.md)
- [Frontend Development Guide](./FRONTEND.md)
- [Deployment Guide](./DEPLOYMENT.md)
- [Troubleshooting Guide](./TROUBLESHOOTING.md)
