# Clinical Metabolomics Oracle - Usage Guide

Complete guide for using the Clinical Metabolomics Oracle application.

## 📋 Overview

The Clinical Metabolomics Oracle (CMO) is an AI-powered platform designed to assist researchers, clinicians, and students in understanding and analyzing clinical metabolomics data. The system provides intelligent chat-based interactions with domain expertise in metabolomics research.

## 🚀 Getting Started

### First Time Setup

1. **Access the Application**
   - Open your web browser
   - Navigate to: http://localhost:3000 (development) or your deployed URL
   - You should see the CMO welcome page

2. **Create an Account**
   - Click "Register" or "Sign Up"
   - Enter your email address
   - Create a secure password (minimum 8 characters)
   - Select your preferred language (English/Spanish)
   - Click "Create Account"

3. **Verify Your Account**
   - Check your email for verification (if email verification is enabled)
   - Click the verification link
   - Return to the application and log in

4. **First Login**
   - Enter your email and password
   - Click "Login"
   - You'll be redirected to the main dashboard

## 🎯 Core Features

### AI-Powered Chat Interface

The main feature of CMO is the intelligent chat system that can help with:

- **Metabolomics Fundamentals**: Basic concepts, terminology, and principles
- **Clinical Applications**: Disease biomarkers, diagnostic applications
- **Data Analysis**: Statistical methods, pathway analysis, interpretation
- **Research Design**: Study planning, sample collection, methodology
- **Literature Review**: Current research, best practices, guidelines

#### Starting a Chat

1. **Navigate to Chat**
   - Click "Chat" in the navigation menu
   - Or click "Start New Chat" from the dashboard

2. **Ask Your Question**
   - Type your question in the message box
   - Examples:
     - "What is metabolomics and how is it used in clinical research?"
     - "How do I analyze metabolomics data for biomarker discovery?"
     - "What are the best practices for sample collection in metabolomics?"

3. **Receive AI Response**
   - The AI assistant will provide a detailed, streaming response
   - Responses are tailored to clinical metabolomics context
   - Follow-up questions are encouraged

#### Chat Features

- **Real-time Streaming**: Responses appear as they're generated
- **Context Awareness**: AI remembers conversation history
- **Multi-language Support**: Available in English and Spanish
- **Thread Management**: Conversations are organized in threads
- **Export Options**: Save conversations for later reference

### Chat History Management

#### Viewing Chat History

1. **Access History**
   - Click "History" in the navigation menu
   - View all your previous chat threads

2. **Thread Organization**
   - Threads are listed chronologically
   - Each thread shows:
     - Title (auto-generated from first message)
     - Creation date
     - Last updated time
     - Message count

3. **Continue Conversations**
   - Click on any thread to continue the conversation
   - Add new messages to existing threads
   - Context from previous messages is maintained

#### Managing Threads

- **Search Threads**: Use the search box to find specific conversations
- **Filter by Date**: Sort threads by creation or update date
- **Delete Threads**: Remove conversations you no longer need
- **Export Threads**: Download conversation history as text or PDF

### User Account Management

#### Profile Settings

1. **Access Account Settings**
   - Click your profile icon or "Account" in the menu
   - View and edit your profile information

2. **Available Settings**
   - **Email Address**: Update your login email
   - **Password**: Change your password
   - **Preferred Language**: Switch between English and Spanish
   - **Notification Preferences**: Configure email notifications
   - **Data Export**: Download your data

#### Language Switching

1. **Change Language**
   - Use the language switcher in the top navigation
   - Or update in account settings
   - Interface immediately updates to selected language

2. **Supported Languages**
   - **English**: Full feature support
   - **Spanish**: Complete translation available
   - **Additional languages**: Can be added upon request

## 💡 Usage Examples

### Research Planning

**Question**: "I'm planning a metabolomics study to identify biomarkers for Type 2 diabetes. What should I consider?"

**Expected Response**: The AI will provide guidance on:
- Study design considerations
- Sample size calculations
- Patient recruitment criteria
- Sample collection protocols
- Analytical platforms to consider
- Statistical analysis approaches
- Validation strategies

### Data Analysis Help

**Question**: "I have LC-MS metabolomics data with 500 samples and 1000 metabolites. How should I approach the statistical analysis?"

**Expected Response**: Detailed guidance on:
- Data preprocessing steps
- Quality control measures
- Statistical methods for biomarker discovery
- Multiple testing corrections
- Pathway analysis approaches
- Visualization techniques
- Interpretation guidelines

### Educational Queries

**Question**: "Can you explain the difference between targeted and untargeted metabolomics?"

**Expected Response**: Comprehensive explanation covering:
- Definitions and principles
- Advantages and disadvantages
- When to use each approach
- Technical considerations
- Examples of applications
- Current best practices

### Clinical Applications

**Question**: "What are the current clinical applications of metabolomics in cancer research?"

**Expected Response**: Overview including:
- Current FDA-approved metabolomics tests
- Biomarkers in clinical trials
- Diagnostic applications
- Prognostic markers
- Treatment monitoring
- Personalized medicine applications

## 🔧 Advanced Features

### API Integration

For developers and researchers who want to integrate CMO into their workflows:

#### Authentication

```bash
# Login to get access token
curl -X POST http://localhost:8000/auth/token \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=<EMAIL>&password=yourpassword"
```

#### Chat API

```bash
# Send a message
curl -X POST http://localhost:8000/chat/ \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"message": "What is metabolomics?", "thread_id": null}'
```

#### Batch Processing

For processing multiple queries:

```python
import requests

# Setup session
session = requests.Session()
session.headers.update({'Authorization': 'Bearer YOUR_TOKEN'})

# Process multiple questions
questions = [
    "What is metabolomics?",
    "How do I analyze metabolomics data?",
    "What are metabolomics biomarkers?"
]

for question in questions:
    response = session.post('http://localhost:8000/chat/', 
                          json={'message': question})
    print(f"Q: {question}")
    print(f"A: {response.json()}")
```

### Data Export

#### Export Chat History

1. **Individual Threads**
   - Open any chat thread
   - Click "Export" button
   - Choose format: Text, PDF, or JSON
   - Download file to your device

2. **Bulk Export**
   - Go to Account Settings
   - Click "Export All Data"
   - Receive download link via email
   - Includes all conversations and metadata

#### Export Formats

- **Text (.txt)**: Plain text format for easy reading
- **PDF (.pdf)**: Formatted document with timestamps
- **JSON (.json)**: Structured data for programmatic use
- **CSV (.csv)**: Tabular format for analysis

### Integration with Research Tools

#### Jupyter Notebook Integration

```python
# Install CMO Python client
pip install cmo-client

# Use in Jupyter
from cmo_client import CMOClient

client = CMOClient(api_key="your_api_key")
response = client.ask("How do I normalize metabolomics data?")
print(response)
```

#### R Integration

```r
# Install CMO R package
install.packages("cmoR")

# Use in R
library(cmoR)
client <- cmo_client(api_key = "your_api_key")
response <- ask_cmo("What statistical tests should I use for metabolomics?")
cat(response)
```

## 📊 Best Practices

### Effective Question Formulation

#### Good Questions

✅ **Specific and Contextual**
- "How should I preprocess LC-MS metabolomics data for biomarker discovery in a case-control study with 200 samples?"

✅ **Include Relevant Details**
- "I'm analyzing urine metabolomics data from diabetic patients. What normalization methods are most appropriate?"

✅ **Clear Objectives**
- "What are the key quality control metrics I should monitor in an untargeted metabolomics experiment?"

#### Questions to Avoid

❌ **Too Vague**
- "How do I do metabolomics?"

❌ **Outside Scope**
- "How do I fix my mass spectrometer?"

❌ **Requesting Specific Software Code**
- "Write me a complete R script for my analysis"

### Conversation Management

#### Organizing Threads

- **Use Descriptive Titles**: Rename threads to reflect their content
- **Group Related Topics**: Keep similar questions in the same thread
- **Regular Cleanup**: Archive or delete old conversations
- **Export Important Insights**: Save valuable responses for future reference

#### Follow-up Strategies

- **Ask for Clarification**: If responses are unclear, ask for more details
- **Request Examples**: Ask for specific examples or case studies
- **Seek Alternatives**: Ask about different approaches or methods
- **Validate Understanding**: Summarize what you learned and ask for confirmation

## 🔒 Privacy and Security

### Data Handling

- **Conversation Privacy**: Your chats are private and not shared with other users
- **Data Encryption**: All communications are encrypted in transit and at rest
- **No Personal Health Information**: Avoid sharing patient-specific data
- **Research Data**: Don't upload proprietary or confidential research data

### Account Security

- **Strong Passwords**: Use unique, complex passwords
- **Regular Updates**: Change passwords periodically
- **Secure Access**: Log out when using shared computers
- **Monitor Activity**: Review your account activity regularly

## 🆘 Getting Help

### Common Issues

#### Login Problems
- **Forgot Password**: Use "Forgot Password" link on login page
- **Account Locked**: Contact support if account is locked
- **Email Verification**: Check spam folder for verification emails

#### Chat Issues
- **Slow Responses**: Check internet connection and try again
- **Error Messages**: Refresh page and retry
- **Missing History**: Ensure you're logged into the correct account

#### Technical Support

1. **Check Documentation**: Review this guide and troubleshooting docs
2. **Search FAQ**: Look for answers in frequently asked questions
3. **Contact Support**: Use the support form or email
4. **Report Bugs**: Use the bug report feature for technical issues

### Feature Requests

- **Suggest Improvements**: Use the feedback form to suggest new features
- **Vote on Features**: Participate in feature voting when available
- **Beta Testing**: Join beta programs to test new features early

## 📚 Educational Resources

### Learning Metabolomics

The CMO can help you learn about:

- **Basic Concepts**: Metabolites, pathways, analytical techniques
- **Study Design**: Planning experiments, controls, sample size
- **Data Analysis**: Statistics, bioinformatics, interpretation
- **Clinical Applications**: Biomarkers, diagnostics, therapeutics
- **Current Research**: Latest developments and trends

### Recommended Learning Path

1. **Start with Basics**: Ask about fundamental concepts
2. **Explore Applications**: Learn about clinical uses
3. **Understand Methods**: Study analytical techniques
4. **Practice Analysis**: Work through data analysis examples
5. **Stay Current**: Ask about recent developments

### External Resources

The AI can recommend:
- **Scientific Papers**: Key publications in metabolomics
- **Textbooks**: Comprehensive learning materials
- **Online Courses**: Educational programs and MOOCs
- **Conferences**: Important meetings and workshops
- **Software Tools**: Analysis platforms and packages

## 🔄 Updates and Maintenance

### System Updates

- **Automatic Updates**: The system updates automatically
- **Feature Announcements**: New features are announced in-app
- **Maintenance Windows**: Scheduled maintenance is announced in advance
- **Version History**: Track changes and improvements

### Staying Informed

- **Newsletter**: Subscribe to updates and tips
- **Release Notes**: Read about new features and fixes
- **Community Forum**: Participate in user discussions
- **Social Media**: Follow for news and updates

## 📞 Support and Contact

### Getting Help

- **Documentation**: Start with this guide and other docs
- **FAQ**: Check frequently asked questions
- **Support Email**: <EMAIL>
- **Bug Reports**: Use in-app bug reporting feature

### Community

- **User Forum**: Connect with other researchers
- **Feature Requests**: Suggest improvements
- **Best Practices**: Share tips and experiences
- **Success Stories**: Share how CMO helped your research

---

*This guide is regularly updated. For the latest version, check the documentation section of the application.*
