# Clinical Metabolomics Oracle - Troubleshooting Guide

Comprehensive troubleshooting guide for common issues and their solutions.

## 📋 Quick Diagnostics

### Health Check Commands

```bash
# Check all services status
docker-compose ps

# Check service logs
docker-compose logs -f [service_name]

# Test connectivity
curl http://localhost:8000/health  # Backend
curl http://localhost:3000         # Frontend

# Check resource usage
docker stats

# Verify environment variables
docker-compose config
```

### Common Log Locations

```bash
# Docker logs
docker-compose logs backend
docker-compose logs frontend
docker-compose logs postgres
docker-compose logs redis

# System logs (if running locally)
tail -f /var/log/nginx/error.log
tail -f /var/log/postgresql/postgresql-15-main.log
```

## 🐳 Docker Issues

### Container Won't Start

**Symptoms:**
- Container exits immediately
- "Exited (1)" status
- Port binding errors

**Diagnosis:**
```bash
# Check container logs
docker-compose logs [service_name]

# Check port conflicts
sudo lsof -i :8000  # Backend port
sudo lsof -i :3000  # Frontend port
sudo lsof -i :5433  # PostgreSQL port

# Check disk space
df -h
docker system df
```

**Solutions:**
```bash
# Kill conflicting processes
sudo kill -9 $(sudo lsof -t -i:8000)

# Clean up Docker resources
docker system prune -a
docker volume prune

# Rebuild containers
docker-compose build --no-cache
docker-compose up -d
```

### Permission Denied Errors

**Symptoms:**
- "Permission denied" in logs
- nginx: [emerg] open() "/var/run/nginx.pid" failed

**Solutions:**
```bash
# Fix file permissions
sudo chown -R $USER:$USER .
chmod +x scripts/*.sh

# Fix Docker socket permissions
sudo chmod 666 /var/run/docker.sock

# For nginx PID file issue (already fixed in our config)
# Ensure nginx.conf uses: pid /tmp/nginx.pid;
```

### Build Failures

**Symptoms:**
- "failed to solve" errors
- Package installation failures
- Out of space errors

**Solutions:**
```bash
# Clear build cache
docker builder prune -a

# Increase Docker memory/disk
# Docker Desktop -> Settings -> Resources

# Check Dockerfile syntax
docker build --no-cache -t test ./cmo_backend
docker build --no-cache -t test ./cmo_frontend

# Free up space
docker system prune -a --volumes
```

## 🗄️ Database Issues

### Connection Refused

**Symptoms:**
- "connection refused" errors
- "could not connect to server"
- Backend health check fails

**Diagnosis:**
```bash
# Check PostgreSQL container
docker-compose ps postgres
docker-compose logs postgres

# Test direct connection
docker-compose exec postgres psql -U postgres -d cmo_db -c "SELECT 1;"

# Check network connectivity
docker-compose exec backend ping postgres
```

**Solutions:**
```bash
# Restart PostgreSQL
docker-compose restart postgres

# Check environment variables
echo $POSTGRES_PASSWORD
docker-compose exec backend env | grep DATABASE_URL

# Reset database
docker-compose down -v
docker-compose up -d postgres
# Wait for startup, then run migrations
docker-compose exec backend alembic upgrade head
```

### Migration Failures

**Symptoms:**
- "relation does not exist" errors
- Alembic revision conflicts
- Schema mismatch errors

**Solutions:**
```bash
# Check migration status
docker-compose exec backend alembic current
docker-compose exec backend alembic history

# Reset migrations (CAUTION: Data loss)
docker-compose exec backend alembic downgrade base
docker-compose exec backend alembic upgrade head

# Manual migration
docker-compose exec postgres psql -U postgres -d cmo_db
# Run SQL commands manually

# Generate new migration
docker-compose exec backend alembic revision --autogenerate -m "Fix schema"
```

### Performance Issues

**Symptoms:**
- Slow query responses
- High CPU usage
- Connection timeouts

**Diagnosis:**
```sql
-- Check active connections
SELECT count(*) FROM pg_stat_activity;

-- Check slow queries
SELECT query, mean_exec_time, calls 
FROM pg_stat_statements 
ORDER BY mean_exec_time DESC 
LIMIT 10;

-- Check table sizes
SELECT schemaname,tablename,attname,n_distinct,correlation 
FROM pg_stats;
```

**Solutions:**
```sql
-- Add indexes
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_chat_threads_user_id ON chat_threads(user_id);

-- Analyze tables
ANALYZE;

-- Vacuum tables
VACUUM ANALYZE;
```

## 🔧 Backend API Issues

### OpenAI API Errors

**Symptoms:**
- "Invalid API key" errors
- "Assistant not found" errors
- Chat responses fail

**Diagnosis:**
```bash
# Check API key
echo $OPENAI_API_KEY

# Test API key
curl -H "Authorization: Bearer $OPENAI_API_KEY" \
     https://api.openai.com/v1/models

# Check assistant ID
curl -H "Authorization: Bearer $OPENAI_API_KEY" \
     https://api.openai.com/v1/assistants/$CMO_ASSISTANT_ID
```

**Solutions:**
```bash
# Update API key
# Edit .env file with new key
docker-compose restart backend

# Create new assistant
# Use OpenAI platform to create assistant
# Update CMO_ASSISTANT_ID in .env

# Check rate limits
# Monitor OpenAI usage dashboard
# Implement exponential backoff
```

### Authentication Issues

**Symptoms:**
- "Invalid token" errors
- Login failures
- Token refresh failures

**Diagnosis:**
```bash
# Check JWT configuration
docker-compose exec backend python -c "
from app.core.config import settings
print(f'JWT Secret: {settings.JWT_SECRET_KEY[:10]}...')
print(f'Token expire: {settings.ACCESS_TOKEN_EXPIRE_MINUTES}')
"

# Test token generation
curl -X POST http://localhost:8000/auth/token \
     -H "Content-Type: application/x-www-form-urlencoded" \
     -d "username=<EMAIL>&password=test123"
```

**Solutions:**
```bash
# Reset JWT secret
# Generate new secret key
python -c "import secrets; print(secrets.token_urlsafe(32))"
# Update JWT_SECRET_KEY in .env
docker-compose restart backend

# Clear token blocklist
docker-compose exec redis redis-cli FLUSHDB

# Check user account status
docker-compose exec backend python -c "
import asyncio
from app.core.database import get_db
from app.crud.user import get_user_by_email

async def check_user():
    async for db in get_db():
        user = await get_user_by_email(db, '<EMAIL>')
        print(f'User active: {user.is_active if user else None}')

asyncio.run(check_user())
"
```

### CORS Errors

**Symptoms:**
- "CORS policy" errors in browser
- Frontend can't reach backend
- Preflight request failures

**Solutions:**
```bash
# Check CORS configuration
docker-compose exec backend python -c "
from app.core.config import settings
print(f'Allowed origins: {settings.ALLOWED_ORIGINS}')
"

# Update CORS origins
# Edit .env file:
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:5173,https://yourdomain.com

# Restart backend
docker-compose restart backend

# Test CORS
curl -H "Origin: http://localhost:3000" \
     -H "Access-Control-Request-Method: POST" \
     -H "Access-Control-Request-Headers: X-Requested-With" \
     -X OPTIONS \
     http://localhost:8000/auth/login
```

## 🎨 Frontend Issues

### Build Failures

**Symptoms:**
- TypeScript compilation errors
- Vite build failures
- Module not found errors

**Diagnosis:**
```bash
# Check Node.js version
node --version  # Should be 18+
npm --version

# Check dependencies
cd cmo_frontend
npm ls

# Check TypeScript
npx tsc --noEmit
```

**Solutions:**
```bash
# Clear cache and reinstall
rm -rf node_modules package-lock.json
npm cache clean --force
npm install

# Fix TypeScript errors
npx tsc --noEmit --skipLibCheck

# Update dependencies
npm update
npm audit fix

# Check Vite configuration
npx vite build --debug
```

### Runtime Errors

**Symptoms:**
- White screen of death
- JavaScript errors in console
- Component rendering failures

**Diagnosis:**
```bash
# Check browser console
# Open Developer Tools -> Console

# Check network requests
# Open Developer Tools -> Network

# Check React DevTools
# Install React Developer Tools extension
```

**Solutions:**
```bash
# Enable error boundaries
# Add error boundary components

# Check API connectivity
curl http://localhost:8000/health

# Verify environment variables
echo $VITE_API_BASE_URL

# Clear browser cache
# Hard refresh: Ctrl+Shift+R
```

### API Connection Issues

**Symptoms:**
- "Network Error" messages
- Failed API requests
- Timeout errors

**Solutions:**
```bash
# Check API base URL
# Verify VITE_API_BASE_URL in .env.local

# Test API connectivity
curl http://localhost:8000/health

# Check browser network tab
# Look for failed requests

# Verify CORS configuration
# Check backend ALLOWED_ORIGINS
```

## 🔗 Network Issues

### Service Discovery Problems

**Symptoms:**
- "host not found" errors
- Services can't communicate
- DNS resolution failures

**Solutions:**
```bash
# Check Docker network
docker network ls
docker network inspect cmo_fastapi_react_default

# Test service connectivity
docker-compose exec frontend ping backend
docker-compose exec backend ping postgres

# Restart networking
docker-compose down
docker-compose up -d
```

### Port Conflicts

**Symptoms:**
- "Port already in use" errors
- Services won't start
- Connection refused on expected ports

**Solutions:**
```bash
# Find conflicting processes
sudo lsof -i :8000
sudo lsof -i :3000
sudo lsof -i :5433

# Kill conflicting processes
sudo kill -9 <PID>

# Use different ports
# Edit docker-compose.yml ports section
# Update environment variables accordingly
```

## 🔍 Performance Issues

### Slow Response Times

**Symptoms:**
- API requests take >5 seconds
- Frontend loading slowly
- Database queries timeout

**Diagnosis:**
```bash
# Check resource usage
docker stats

# Monitor API response times
curl -w "@curl-format.txt" -o /dev/null -s http://localhost:8000/health

# Check database performance
docker-compose exec postgres psql -U postgres -d cmo_db -c "
SELECT query, mean_exec_time, calls 
FROM pg_stat_statements 
ORDER BY mean_exec_time DESC 
LIMIT 5;
"
```

**Solutions:**
```bash
# Increase container resources
# Edit docker-compose.yml:
# deploy:
#   resources:
#     limits:
#       memory: 2G
#       cpus: '1.0'

# Optimize database
docker-compose exec postgres psql -U postgres -d cmo_db -c "VACUUM ANALYZE;"

# Add Redis caching
# Verify Redis is running and configured

# Optimize frontend bundle
cd cmo_frontend
npm run build -- --analyze
```

### Memory Issues

**Symptoms:**
- Out of memory errors
- Container restarts
- System becomes unresponsive

**Solutions:**
```bash
# Check memory usage
free -h
docker stats

# Increase Docker memory limit
# Docker Desktop -> Settings -> Resources

# Add swap space (Linux)
sudo fallocate -l 2G /swapfile
sudo chmod 600 /swapfile
sudo mkswap /swapfile
sudo swapon /swapfile

# Optimize application memory usage
# Review code for memory leaks
# Implement pagination for large datasets
```

## 🛠️ Development Environment Issues

### Hot Reload Not Working

**Symptoms:**
- Changes don't reflect automatically
- Need to restart containers manually
- File watching not working

**Solutions:**
```bash
# Check volume mounts
docker-compose config

# Restart development server
docker-compose restart frontend

# Check file permissions
ls -la cmo_frontend/src/

# For Windows/WSL issues
# Use polling instead of file watching
# Add to vite.config.js:
# server: { watch: { usePolling: true } }
```

### IDE Integration Issues

**Symptoms:**
- TypeScript errors in IDE
- Import resolution failures
- Debugging not working

**Solutions:**
```bash
# Restart TypeScript server in IDE
# VS Code: Ctrl+Shift+P -> "TypeScript: Restart TS Server"

# Check tsconfig.json paths
# Verify baseUrl and paths configuration

# Install workspace extensions
# ESLint, Prettier, TypeScript Hero

# Configure debugger
# Add .vscode/launch.json for debugging
```

## 📞 Getting Help

### Diagnostic Information to Collect

When reporting issues, include:

```bash
# System information
uname -a
docker --version
docker-compose --version

# Service status
docker-compose ps
docker-compose logs --tail=50 [service_name]

# Configuration
docker-compose config
env | grep -E "(POSTGRES|REDIS|OPENAI|JWT)"

# Resource usage
docker stats --no-stream
df -h
free -h
```

### Log Analysis

```bash
# Search for specific errors
docker-compose logs | grep -i error
docker-compose logs | grep -i "failed"
docker-compose logs | grep -i "exception"

# Filter by service and time
docker-compose logs --since="1h" backend
docker-compose logs --tail=100 frontend

# Export logs for analysis
docker-compose logs > debug.log
```

### Community Resources

- **GitHub Issues**: Report bugs and feature requests
- **Documentation**: Check latest docs for updates
- **Stack Overflow**: Search for similar issues
- **Docker Hub**: Check for image updates

### Emergency Procedures

```bash
# Complete system reset (CAUTION: Data loss)
docker-compose down -v
docker system prune -a
docker volume prune
# Restore from backup
./scripts/restore-backup.sh [backup_date]

# Quick service restart
docker-compose restart

# Rollback to previous version
git checkout HEAD~1
./deploy.sh
```

## 📚 Additional Resources

- [Docker Troubleshooting](https://docs.docker.com/config/troubleshooting/)
- [PostgreSQL Common Errors](https://www.postgresql.org/docs/current/errcodes-appendix.html)
- [FastAPI Debugging](https://fastapi.tiangolo.com/tutorial/debugging/)
- [React Error Boundaries](https://reactjs.org/docs/error-boundaries.html)
- [Nginx Troubleshooting](https://nginx.org/en/docs/debugging_log.html)
