# Clinical Metabolomics Oracle - Frontend Development Guide

Comprehensive guide for developing and customizing the CMO React frontend application.

## 📋 Overview

The CMO frontend is a modern React application built with:
- **React 19** with TypeScript
- **Vite** for fast development and building
- **Redux Toolkit** for state management
- **React Router** for navigation
- **i18next** for internationalization
- **Axios** for API communication

## 🏗️ Architecture

### Project Structure

```
cmo_frontend/
├── public/                 # Static assets
│   ├── vite.svg           # Favicon
│   └── locales/           # Translation files
│       ├── en/
│       └── es/
├── src/
│   ├── api/               # API client configuration
│   │   └── client.ts      # Axios configuration
│   ├── app/               # App-level configuration
│   │   ├── router.tsx     # React Router setup
│   │   └── store.ts       # Redux store configuration
│   ├── components/        # Reusable UI components
│   │   ├── AdminRouteLayout.tsx
│   │   ├── LanguageSwitcher.tsx
│   │   └── ProtectedRouteLayout.tsx
│   ├── features/          # Feature-specific components
│   │   ├── auth/          # Authentication components
│   │   │   ├── authSlice.ts
│   │   │   ├── LoginForm.tsx
│   │   │   └── RegisterForm.tsx
│   │   └── chat/          # Chat components
│   │       ├── chatSlice.ts
│   │       ├── ChatInterface.tsx
│   │       ├── ChatHistory.tsx
│   │       └── MessageList.tsx
│   ├── hooks/             # Custom React hooks
│   │   └── useLanguageSync.ts
│   ├── pages/             # Page components
│   │   ├── AccountPage.tsx
│   │   ├── ChatHistoryPage.tsx
│   │   ├── ChatPage.tsx
│   │   ├── LoginPage.tsx
│   │   └── RegistrationPage.tsx
│   ├── styles/            # Global styles and themes
│   │   └── globals.css
│   ├── lib/               # Utility functions
│   ├── App.tsx            # Main App component
│   ├── main.tsx           # Application entry point
│   └── i18n.ts            # Internationalization setup
├── index.html             # HTML template
├── package.json           # Dependencies and scripts
├── tsconfig.json          # TypeScript configuration
├── vite.config.js         # Vite configuration
└── eslint.config.js       # ESLint configuration
```

### Component Architecture

```
App
├── Router
│   ├── PublicRoutes
│   │   ├── LoginPage
│   │   └── RegistrationPage
│   ├── ProtectedRoutes
│   │   ├── ChatPage
│   │   ├── ChatHistoryPage
│   │   └── AccountPage
│   └── AdminRoutes
│       └── AdminDashboard
└── GlobalComponents
    ├── LanguageSwitcher
    └── ErrorBoundary
```

## 🚀 Getting Started

### Development Setup

```bash
# Navigate to frontend directory
cd cmo_frontend

# Install dependencies
npm install

# Start development server
npm run dev

# Access application
# http://localhost:5173
```

### Available Scripts

```bash
# Development
npm run dev          # Start dev server with hot reload
npm run build        # Build for production
npm run preview      # Preview production build
npm run lint         # Run ESLint
npm run type-check   # Run TypeScript compiler check
```

### Environment Configuration

Create `.env.local` for local development:

```bash
# API Configuration
VITE_API_BASE_URL=http://localhost:8000
VITE_API_TIMEOUT=30000

# Feature Flags
VITE_ENABLE_CHAT=true
VITE_ENABLE_ANALYTICS=false

# Development
VITE_DEBUG_MODE=true
```

## 🔧 Core Features

### Authentication System

#### Auth Slice (Redux)
```typescript
// src/features/auth/authSlice.ts
import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';

interface AuthState {
  user: User | null;
  tokens: TokenPair | null;
  isLoading: boolean;
  error: string | null;
}

export const loginUser = createAsyncThunk(
  'auth/login',
  async (credentials: LoginCredentials) => {
    const response = await authAPI.login(credentials);
    return response.data;
  }
);

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    logout: (state) => {
      state.user = null;
      state.tokens = null;
      localStorage.removeItem('tokens');
    },
    clearError: (state) => {
      state.error = null;
    }
  },
  extraReducers: (builder) => {
    builder
      .addCase(loginUser.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(loginUser.fulfilled, (state, action) => {
        state.isLoading = false;
        state.user = action.payload.user;
        state.tokens = action.payload.tokens;
      })
      .addCase(loginUser.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Login failed';
      });
  }
});
```

#### Login Component
```typescript
// src/features/auth/LoginForm.tsx
import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { loginUser } from './authSlice';

export const LoginForm: React.FC = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const dispatch = useDispatch();
  const { isLoading, error } = useSelector((state: RootState) => state.auth);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    await dispatch(loginUser({ email, password }));
  };

  return (
    <form onSubmit={handleSubmit} className="login-form">
      <div className="form-group">
        <label htmlFor="email">Email</label>
        <input
          id="email"
          type="email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          required
        />
      </div>
      
      <div className="form-group">
        <label htmlFor="password">Password</label>
        <input
          id="password"
          type="password"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          required
        />
      </div>

      {error && <div className="error-message">{error}</div>}
      
      <button type="submit" disabled={isLoading}>
        {isLoading ? 'Logging in...' : 'Login'}
      </button>
    </form>
  );
};
```

### Chat System

#### Chat Interface Component
```typescript
// src/features/chat/ChatInterface.tsx
import React, { useState, useRef, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { sendMessage, addMessage } from './chatSlice';

export const ChatInterface: React.FC = () => {
  const [message, setMessage] = useState('');
  const [isStreaming, setIsStreaming] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const dispatch = useDispatch();
  
  const { currentThread, messages, isLoading } = useSelector(
    (state: RootState) => state.chat
  );

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSendMessage = async () => {
    if (!message.trim() || isStreaming) return;

    const userMessage = {
      content: message,
      sender: 'user' as const,
      timestamp: new Date().toISOString()
    };

    dispatch(addMessage(userMessage));
    setMessage('');
    setIsStreaming(true);

    try {
      // Start SSE connection for streaming response
      const eventSource = new EventSource('/api/chat/', {
        headers: {
          'Authorization': `Bearer ${tokens.access_token}`
        }
      });

      let aiResponse = '';

      eventSource.onmessage = (event) => {
        const data = JSON.parse(event.data);
        
        switch (data.type) {
          case 'token':
            aiResponse += data.content;
            dispatch(updateStreamingMessage(aiResponse));
            break;
          case 'complete':
            eventSource.close();
            setIsStreaming(false);
            break;
          case 'error':
            console.error('Chat error:', data.message);
            eventSource.close();
            setIsStreaming(false);
            break;
        }
      };

      eventSource.onerror = () => {
        eventSource.close();
        setIsStreaming(false);
      };

    } catch (error) {
      console.error('Failed to send message:', error);
      setIsStreaming(false);
    }
  };

  return (
    <div className="chat-interface">
      <div className="messages-container">
        {messages.map((msg, index) => (
          <div key={index} className={`message ${msg.sender}`}>
            <div className="message-content">{msg.content}</div>
            <div className="message-timestamp">
              {new Date(msg.timestamp).toLocaleTimeString()}
            </div>
          </div>
        ))}
        <div ref={messagesEndRef} />
      </div>

      <div className="input-container">
        <textarea
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          placeholder="Ask about metabolomics..."
          onKeyDown={(e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
              e.preventDefault();
              handleSendMessage();
            }
          }}
          disabled={isStreaming}
        />
        <button 
          onClick={handleSendMessage}
          disabled={!message.trim() || isStreaming}
        >
          {isStreaming ? 'Sending...' : 'Send'}
        </button>
      </div>
    </div>
  );
};
```

### Internationalization

#### i18n Configuration
```typescript
// src/i18n.ts
import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';
import Backend from 'i18next-http-backend';

i18n
  .use(Backend)
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    fallbackLng: 'en',
    debug: import.meta.env.DEV,
    
    interpolation: {
      escapeValue: false
    },

    backend: {
      loadPath: '/locales/{{lng}}/{{ns}}.json'
    },

    detection: {
      order: ['localStorage', 'navigator', 'htmlTag'],
      caches: ['localStorage']
    }
  });

export default i18n;
```

#### Translation Files
```json
// public/locales/en/common.json
{
  "navigation": {
    "home": "Home",
    "chat": "Chat",
    "history": "History",
    "account": "Account",
    "logout": "Logout"
  },
  "auth": {
    "login": "Login",
    "register": "Register",
    "email": "Email",
    "password": "Password",
    "confirmPassword": "Confirm Password",
    "forgotPassword": "Forgot Password?",
    "loginSuccess": "Login successful",
    "loginError": "Invalid email or password"
  },
  "chat": {
    "placeholder": "Ask about metabolomics...",
    "send": "Send",
    "newChat": "New Chat",
    "chatHistory": "Chat History",
    "noMessages": "No messages yet",
    "streamingError": "Error receiving response"
  }
}
```

#### Using Translations
```typescript
// In components
import { useTranslation } from 'react-i18next';

export const NavigationBar: React.FC = () => {
  const { t } = useTranslation('common');

  return (
    <nav>
      <a href="/chat">{t('navigation.chat')}</a>
      <a href="/history">{t('navigation.history')}</a>
      <a href="/account">{t('navigation.account')}</a>
    </nav>
  );
};
```

### API Client

#### Axios Configuration
```typescript
// src/api/client.ts
import axios, { AxiosResponse, AxiosError } from 'axios';
import { store } from '../app/store';
import { logout, refreshTokens } from '../features/auth/authSlice';

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000';

export const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json'
  }
});

// Request interceptor to add auth token
apiClient.interceptors.request.use(
  (config) => {
    const state = store.getState();
    const token = state.auth.tokens?.access_token;
    
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    
    return config;
  },
  (error) => Promise.reject(error)
);

// Response interceptor for token refresh
apiClient.interceptors.response.use(
  (response: AxiosResponse) => response,
  async (error: AxiosError) => {
    const originalRequest = error.config;
    
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;
      
      try {
        await store.dispatch(refreshTokens());
        const newToken = store.getState().auth.tokens?.access_token;
        
        if (newToken) {
          originalRequest.headers.Authorization = `Bearer ${newToken}`;
          return apiClient(originalRequest);
        }
      } catch (refreshError) {
        store.dispatch(logout());
        window.location.href = '/login';
      }
    }
    
    return Promise.reject(error);
  }
);

// API methods
export const authAPI = {
  login: (credentials: LoginCredentials) =>
    apiClient.post('/auth/token', credentials),
  
  register: (userData: RegisterData) =>
    apiClient.post('/auth/register', userData),
  
  refresh: (refreshToken: string) =>
    apiClient.post('/auth/refresh', { refresh_token: refreshToken }),
  
  logout: (refreshToken: string) =>
    apiClient.post('/auth/logout', { refresh_token: refreshToken })
};

export const chatAPI = {
  sendMessage: (message: string, threadId?: number) =>
    apiClient.post('/chat/', { message, thread_id: threadId }),
  
  getHistory: (skip = 0, limit = 10) =>
    apiClient.get(`/chat/history?skip=${skip}&limit=${limit}`),
  
  getThread: (threadId: number) =>
    apiClient.get(`/chat/history/${threadId}`)
};

export const userAPI = {
  getCurrentUser: () => apiClient.get('/users/me'),
  
  updateProfile: (data: Partial<UserProfile>) =>
    apiClient.put('/users/me', data)
};
```

## 🎨 Styling and Theming

### CSS Architecture

```css
/* src/styles/globals.css */
:root {
  /* Color Palette */
  --color-primary: #2563eb;
  --color-primary-dark: #1d4ed8;
  --color-secondary: #64748b;
  --color-success: #10b981;
  --color-warning: #f59e0b;
  --color-error: #ef4444;
  
  /* Typography */
  --font-family-sans: 'Inter', system-ui, sans-serif;
  --font-family-mono: 'Fira Code', monospace;
  
  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  
  /* Borders */
  --border-radius: 0.375rem;
  --border-width: 1px;
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  :root {
    --color-bg: #0f172a;
    --color-surface: #1e293b;
    --color-text: #f8fafc;
    --color-text-muted: #94a3b8;
  }
}

/* Component styles */
.chat-interface {
  display: flex;
  flex-direction: column;
  height: 100vh;
  max-width: 800px;
  margin: 0 auto;
  padding: var(--spacing-md);
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: var(--spacing-md);
  border: var(--border-width) solid var(--color-border);
  border-radius: var(--border-radius);
  margin-bottom: var(--spacing-md);
}

.message {
  margin-bottom: var(--spacing-md);
  padding: var(--spacing-sm);
  border-radius: var(--border-radius);
}

.message.user {
  background-color: var(--color-primary);
  color: white;
  margin-left: auto;
  max-width: 70%;
}

.message.ai {
  background-color: var(--color-surface);
  color: var(--color-text);
  margin-right: auto;
  max-width: 70%;
}
```

## 🧪 Testing

### Component Testing Setup

```typescript
// src/test/setup.ts
import { expect, afterEach } from 'vitest';
import { cleanup } from '@testing-library/react';
import * as matchers from '@testing-library/jest-dom/matchers';

expect.extend(matchers);

afterEach(() => {
  cleanup();
});
```

### Example Component Test

```typescript
// src/features/auth/LoginForm.test.tsx
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import { LoginForm } from './LoginForm';
import { authSlice } from './authSlice';

const createTestStore = () => {
  return configureStore({
    reducer: {
      auth: authSlice.reducer
    }
  });
};

describe('LoginForm', () => {
  it('renders login form fields', () => {
    const store = createTestStore();
    
    render(
      <Provider store={store}>
        <LoginForm />
      </Provider>
    );

    expect(screen.getByLabelText(/email/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/password/i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /login/i })).toBeInTheDocument();
  });

  it('submits form with valid data', async () => {
    const store = createTestStore();
    
    render(
      <Provider store={store}>
        <LoginForm />
      </Provider>
    );

    fireEvent.change(screen.getByLabelText(/email/i), {
      target: { value: '<EMAIL>' }
    });
    
    fireEvent.change(screen.getByLabelText(/password/i), {
      target: { value: 'password123' }
    });

    fireEvent.click(screen.getByRole('button', { name: /login/i }));

    await waitFor(() => {
      expect(store.getState().auth.isLoading).toBe(true);
    });
  });
});
```

## 🚀 Build and Deployment

### Production Build

```bash
# Build for production
npm run build

# Preview production build
npm run preview

# Analyze bundle size
npm run build -- --analyze
```

### Docker Build

```dockerfile
# Multi-stage build
FROM node:18-alpine as builder

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=builder /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

### Environment Variables for Production

```bash
# Production environment
VITE_API_BASE_URL=https://api.yourdomain.com
VITE_ENABLE_ANALYTICS=true
VITE_SENTRY_DSN=your-sentry-dsn
```

## 🔧 Development Best Practices

### Code Organization
- Use feature-based folder structure
- Keep components small and focused
- Implement proper TypeScript types
- Use custom hooks for reusable logic
- Follow consistent naming conventions

### Performance Optimization
- Implement React.memo for expensive components
- Use useMemo and useCallback appropriately
- Lazy load routes and components
- Optimize bundle size with code splitting
- Implement proper error boundaries

### State Management
- Use Redux Toolkit for complex state
- Keep local state for component-specific data
- Implement proper error handling
- Use RTK Query for API state management

## 📚 Additional Resources

- [React Documentation](https://react.dev)
- [Redux Toolkit Guide](https://redux-toolkit.js.org)
- [Vite Documentation](https://vitejs.dev)
- [TypeScript Handbook](https://www.typescriptlang.org/docs)
- [Testing Library](https://testing-library.com)
