# Clinical Metabolomics Oracle - Installation Guide

This comprehensive guide covers all installation methods for the Clinical Metabolomics Oracle (CMO) application.

## 📋 Prerequisites

### System Requirements

- **Operating System**: Linux, macOS, or Windows 10/11
- **RAM**: Minimum 4GB, Recommended 8GB+
- **Storage**: 10GB free space
- **Network**: Internet connection for downloading dependencies

### Required Software

#### For Docker Installation (Recommended)
- **Docker**: Version 20.0+ 
- **Docker Compose**: Version 2.0+

#### For Local Development
- **Python**: Version 3.11+
- **Node.js**: Version 18+
- **PostgreSQL**: Version 15+
- **Redis**: Version 7+
- **Git**: Latest version

## 🐳 Method 1: Docker Installation (Recommended)

### Step 1: Install Docker

#### On Ubuntu/Debian
```bash
# Update package index
sudo apt update

# Install Docker
sudo apt install docker.io docker-compose-plugin

# Add user to docker group
sudo usermod -aG docker $USER

# Restart session or run
newgrp docker
```

#### On macOS
```bash
# Install Docker Desktop
brew install --cask docker

# Or download from: https://www.docker.com/products/docker-desktop
```

#### On Windows
1. Download Docker Desktop from https://www.docker.com/products/docker-desktop
2. Run installer and follow instructions
3. Enable WSL2 integration if prompted

### Step 2: Clone Repository

```bash
# Clone the repository
git clone <repository-url>
cd cmo_fastapi_react

# Verify structure
ls -la
```

### Step 3: Environment Configuration

```bash
# Create environment file
cp .env.example .env

# Edit configuration
nano .env  # or use your preferred editor
```

#### Required Environment Variables

```bash
# Database Configuration
POSTGRES_USER=postgres
POSTGRES_PASSWORD=your_secure_password_here
POSTGRES_DB=cmo_db

# Security Keys (Generate secure keys)
SECRET_KEY=your-super-secret-key-minimum-32-characters
JWT_SECRET_KEY=your-jwt-secret-key-minimum-32-characters

# OpenAI Configuration (Required for AI features)
OPENAI_API_KEY=sk-your-openai-api-key-here
CMO_ASSISTANT_ID=asst_your-assistant-id-here

# Optional: Custom Ports (if defaults conflict)
POSTGRES_PORT=5433
REDIS_PORT=6380
BACKEND_PORT=8000
FRONTEND_PORT=3000
```

#### Generate Secure Keys

```bash
# Generate SECRET_KEY
python -c "import secrets; print(secrets.token_urlsafe(32))"

# Generate JWT_SECRET_KEY
python -c "import secrets; print(secrets.token_urlsafe(32))"
```

### Step 4: OpenAI Assistant Setup

1. **Create OpenAI Account**
   - Visit https://platform.openai.com
   - Create account and add billing information

2. **Create Assistant**
   ```bash
   # Using OpenAI CLI (optional)
   pip install openai
   
   # Or use the web interface at https://platform.openai.com/assistants
   ```

3. **Configure Assistant**
   - Name: "Clinical Metabolomics Oracle"
   - Instructions: "You are an expert in clinical metabolomics..."
   - Model: gpt-4-turbo or gpt-4
   - Copy the Assistant ID (starts with `asst_`)

### Step 5: Start Services

```bash
# Start all services
docker-compose up -d

# View startup logs
docker-compose logs -f

# Check service status
docker-compose ps
```

Expected output:
```
NAME           IMAGE                        STATUS         PORTS
cmo_backend    cmo_fastapi_react-backend    Up (healthy)   0.0.0.0:8000->8000/tcp
cmo_frontend   cmo_fastapi_react-frontend   Up (healthy)   0.0.0.0:3000->80/tcp
cmo_postgres   postgres:15-alpine           Up (healthy)   0.0.0.0:5433->5432/tcp
cmo_redis      redis:7-alpine               Up (healthy)   0.0.0.0:6380->6379/tcp
```

### Step 6: Database Initialization

```bash
# Run database migrations
docker-compose exec backend alembic upgrade head

# Create admin user (optional)
docker-compose exec backend python -c "
import asyncio
from app.core.database import get_db
from app.crud.user import create_user
from app.models.user import UserCreate, UserRole

async def create_admin():
    async for db in get_db():
        try:
            admin_data = UserCreate(
                email='<EMAIL>',
                password='admin123',
                role=UserRole.ADMIN
            )
            user = await create_user(db, admin_data)
            print(f'Admin user created: {user.email}')
        except Exception as e:
            print(f'Admin user may already exist: {e}')

asyncio.run(create_admin())
"
```

### Step 7: Verify Installation

```bash
# Test backend health
curl http://localhost:8000/health

# Test frontend
curl http://localhost:3000

# Test database connection
docker-compose exec backend python -c "
import asyncio
from app.core.database import engine

async def test_db():
    async with engine.begin() as conn:
        result = await conn.execute('SELECT 1')
        print('Database connected successfully')

asyncio.run(test_db())
"
```

### Step 8: Access Application

- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:8000
- **API Documentation**: http://localhost:8000/docs
- **Database**: localhost:5433 (postgres/your_password)
- **Redis**: localhost:6380

## 💻 Method 2: Local Development Installation

### Step 1: Install System Dependencies

#### On Ubuntu/Debian
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Python 3.11+
sudo apt install python3.11 python3.11-venv python3.11-dev

# Install Node.js 18+
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt install nodejs

# Install PostgreSQL
sudo apt install postgresql postgresql-contrib

# Install Redis
sudo apt install redis-server

# Install Git
sudo apt install git
```

#### On macOS
```bash
# Install Homebrew if not installed
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# Install dependencies
brew install python@3.11 node@18 postgresql@15 redis git
```

#### On Windows
1. Install Python 3.11+ from https://python.org
2. Install Node.js 18+ from https://nodejs.org
3. Install PostgreSQL from https://postgresql.org
4. Install Redis from https://redis.io/download
5. Install Git from https://git-scm.com

### Step 2: Database Setup

#### PostgreSQL Setup
```bash
# Start PostgreSQL service
sudo systemctl start postgresql  # Linux
brew services start postgresql   # macOS

# Create database and user
sudo -u postgres psql
```

```sql
-- In PostgreSQL shell
CREATE USER cmo_user WITH PASSWORD 'your_password';
CREATE DATABASE cmo_db OWNER cmo_user;
GRANT ALL PRIVILEGES ON DATABASE cmo_db TO cmo_user;
\q
```

#### Redis Setup
```bash
# Start Redis service
sudo systemctl start redis-server  # Linux
brew services start redis          # macOS

# Test Redis connection
redis-cli ping
```

### Step 3: Backend Setup

```bash
# Clone repository
git clone <repository-url>
cd cmo_fastapi_react/cmo_backend

# Create virtual environment
python3.11 -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Upgrade pip
pip install --upgrade pip

# Install dependencies
pip install -r requirements.txt

# Create environment file
cp .env.example .env
```

#### Configure Backend Environment
```bash
# Edit .env file
nano .env
```

```bash
# Local development configuration
DATABASE_URL=postgresql+asyncpg://cmo_user:your_password@localhost:5432/cmo_db
REDIS_URL=redis://localhost:6379/0
SECRET_KEY=your-generated-secret-key
JWT_SECRET_KEY=your-generated-jwt-key
ENVIRONMENT=development
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:5173
OPENAI_API_KEY=your-openai-api-key
CMO_ASSISTANT_ID=your-assistant-id
```

#### Initialize Database
```bash
# Run migrations
alembic upgrade head

# Start backend server
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

### Step 4: Frontend Setup

```bash
# Open new terminal
cd cmo_fastapi_react/cmo_frontend

# Install dependencies
npm install

# Create environment file (if needed)
cp .env.example .env.local

# Start development server
npm run dev
```

### Step 5: Verify Local Installation

```bash
# Test backend (new terminal)
curl http://localhost:8000/health

# Test frontend
curl http://localhost:5173  # Vite dev server
```

## 🔧 Post-Installation Configuration

### SSL/TLS Setup (Production)

```bash
# Generate self-signed certificates (development)
openssl req -x509 -newkey rsa:4096 -keyout key.pem -out cert.pem -days 365 -nodes

# Or use Let's Encrypt (production)
sudo apt install certbot
sudo certbot certonly --standalone -d your-domain.com
```

### Reverse Proxy Setup (Nginx)

```nginx
# /etc/nginx/sites-available/cmo
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }

    location /api {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

### Monitoring Setup

```bash
# Install monitoring tools
pip install prometheus-client
npm install @prometheus/client

# Configure health checks
curl http://localhost:8000/health
curl http://localhost:3000/health
```

## 🔍 Troubleshooting Installation

### Common Issues

#### Docker Issues
```bash
# Permission denied
sudo chmod 666 /var/run/docker.sock

# Port conflicts
docker-compose down
sudo lsof -i :8000  # Check what's using port
sudo kill -9 <PID>  # Kill process

# Build failures
docker-compose build --no-cache
docker system prune -a
```

#### Database Issues
```bash
# Connection refused
sudo systemctl status postgresql
sudo systemctl start postgresql

# Authentication failed
sudo -u postgres psql
\password postgres

# Migration errors
alembic downgrade base
alembic upgrade head
```

#### Node.js Issues
```bash
# Version conflicts
nvm install 18
nvm use 18

# Package installation failures
rm -rf node_modules package-lock.json
npm cache clean --force
npm install
```

### Verification Checklist

- [ ] All services start without errors
- [ ] Database migrations complete successfully
- [ ] Frontend loads at http://localhost:3000
- [ ] Backend API responds at http://localhost:8000/health
- [ ] API documentation accessible at http://localhost:8000/docs
- [ ] User registration works
- [ ] Login functionality works
- [ ] Chat interface loads (if OpenAI configured)

### Getting Help

1. **Check logs**: `docker-compose logs -f` or service-specific logs
2. **Verify configuration**: Double-check environment variables
3. **Test connectivity**: Use curl to test endpoints
4. **Review documentation**: Check API docs at `/docs`
5. **Community support**: Create issue in repository

## 🚀 Next Steps

After successful installation:

1. **Configure OpenAI Assistant** for full AI functionality
2. **Set up user accounts** and roles
3. **Customize frontend** branding and styling
4. **Configure monitoring** and logging
5. **Set up backups** for database
6. **Review security settings** for production deployment

## 📚 Additional Resources

- [API Documentation](./API.md)
- [Frontend Development Guide](./FRONTEND.md)
- [Deployment Guide](./DEPLOYMENT.md)
- [Troubleshooting Guide](./TROUBLESHOOTING.md)
