# Clinical Metabolomics Oracle - Environment Configuration
# Copy this file to .env and update with your actual values

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================

# PostgreSQL Database Settings
POSTGRES_USER=postgres
POSTGRES_PASSWORD=your_secure_database_password_here
POSTGRES_DB=cmo_db

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================

# Application Secret Key (minimum 32 characters)
# Generate with: python -c "import secrets; print(secrets.token_urlsafe(32))"
SECRET_KEY=your-super-secret-key-change-in-production-minimum-32-chars

# JWT Secret Key for token signing (minimum 32 characters)
# Generate with: python -c "import secrets; print(secrets.token_urlsafe(32))"
JWT_SECRET_KEY=your-jwt-secret-key-change-in-production-minimum-32-chars

# =============================================================================
# OPENAI CONFIGURATION
# =============================================================================

# OpenAI API Key (required for AI chat functionality)
# Get from: https://platform.openai.com/api-keys
OPENAI_API_KEY=sk-your-openai-api-key-here

# OpenAI Assistant ID (required for chat functionality)
# Create assistant at: https://platform.openai.com/assistants
CMO_ASSISTANT_ID=asst_your-assistant-id-here

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================

# Environment Mode
ENVIRONMENT=development

# CORS Configuration (comma-separated origins)
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:5173

# Frontend API Base URL
VITE_API_BASE_URL=http://localhost:8000

# =============================================================================
# OPTIONAL OVERRIDES
# =============================================================================

# Custom ports (if defaults conflict)
# POSTGRES_PORT=5433
# REDIS_PORT=6380
# BACKEND_PORT=8000
# FRONTEND_PORT=3000

# Database and Redis URLs (auto-generated from above if not specified)
# DATABASE_URL=postgresql+asyncpg://postgres:password@postgres:5432/cmo_db
# REDIS_URL=redis://redis:6379/0

# =============================================================================
# PRODUCTION NOTES
# =============================================================================
# For production deployment:
# 1. Use strong, unique passwords
# 2. Generate secure secret keys
# 3. Set ENVIRONMENT=production
# 4. Configure proper ALLOWED_ORIGINS
# 5. Set up SSL/TLS certificates
# 6. Enable monitoring and logging
