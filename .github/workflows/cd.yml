name: Continuous Deployment

on:
  push:
    branches: [ main ]
  workflow_dispatch:

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  build-and-push:
    name: Build and Push Docker Images
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
      
    - name: Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}
        
    - name: Extract metadata for backend
      id: meta-backend
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/backend
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=sha,prefix={{branch}}-
          type=raw,value=latest,enable={{is_default_branch}}
          
    - name: Extract metadata for frontend
      id: meta-frontend
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/frontend
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=sha,prefix={{branch}}-
          type=raw,value=latest,enable={{is_default_branch}}
          
    - name: Build and push backend image
      uses: docker/build-push-action@v5
      with:
        context: ./cmo_backend
        file: ./cmo_backend/Dockerfile
        push: true
        tags: ${{ steps.meta-backend.outputs.tags }}
        labels: ${{ steps.meta-backend.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max
        platforms: linux/amd64,linux/arm64
        
    - name: Build and push frontend image
      uses: docker/build-push-action@v5
      with:
        context: ./cmo_frontend
        file: ./cmo_frontend/Dockerfile
        push: true
        tags: ${{ steps.meta-frontend.outputs.tags }}
        labels: ${{ steps.meta-frontend.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max
        platforms: linux/amd64,linux/arm64
        
    - name: Generate deployment summary
      run: |
        echo "## 🚀 Deployment Summary" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "### Backend Image" >> $GITHUB_STEP_SUMMARY
        echo "- **Registry**: ${{ env.REGISTRY }}" >> $GITHUB_STEP_SUMMARY
        echo "- **Repository**: ${{ env.IMAGE_NAME }}/backend" >> $GITHUB_STEP_SUMMARY
        echo "- **Tags**: ${{ steps.meta-backend.outputs.tags }}" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "### Frontend Image" >> $GITHUB_STEP_SUMMARY
        echo "- **Registry**: ${{ env.REGISTRY }}" >> $GITHUB_STEP_SUMMARY
        echo "- **Repository**: ${{ env.IMAGE_NAME }}/frontend" >> $GITHUB_STEP_SUMMARY
        echo "- **Tags**: ${{ steps.meta-frontend.outputs.tags }}" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "### Commit Information" >> $GITHUB_STEP_SUMMARY
        echo "- **SHA**: ${{ github.sha }}" >> $GITHUB_STEP_SUMMARY
        echo "- **Branch**: ${{ github.ref_name }}" >> $GITHUB_STEP_SUMMARY
        echo "- **Actor**: ${{ github.actor }}" >> $GITHUB_STEP_SUMMARY

  deploy:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: build-and-push
    if: github.ref == 'refs/heads/main'
    environment: production

    steps:
    - name: Deploy to production server
      uses: appleboy/ssh-action@v1.0.3
      with:
        host: ${{ secrets.PRODUCTION_HOST }}
        username: ${{ secrets.PRODUCTION_USER }}
        key: ${{ secrets.PRODUCTION_SSH_KEY }}
        port: ${{ secrets.PRODUCTION_PORT || 22 }}
        script: |
          # Navigate to project directory
          cd ${{ secrets.PRODUCTION_PROJECT_PATH || '/opt/cmo_fastapi_react' }}

          # Pull latest changes from git
          git pull origin main

          # Log in to GitHub Container Registry
          echo ${{ secrets.GITHUB_TOKEN }} | docker login ghcr.io -u ${{ github.actor }} --password-stdin

          # Pull latest Docker images
          docker pull ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/backend:latest
          docker pull ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/frontend:latest

          # Update docker-compose to use the new images
          export BACKEND_IMAGE=${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/backend:latest
          export FRONTEND_IMAGE=${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/frontend:latest

          # Restart services with new images
          docker-compose down
          docker-compose up -d --force-recreate

          # Clean up old images
          docker image prune -f

          # Verify deployment
          sleep 30
          docker-compose ps

          # Check health endpoints
          curl -f http://localhost:8000/health || echo "Backend health check failed"
          curl -f http://localhost:3000/health || echo "Frontend health check failed"

    - name: Deployment notification
      if: always()
      run: |
        if [ "${{ job.status }}" == "success" ]; then
          echo "✅ Deployment to production completed successfully!" >> $GITHUB_STEP_SUMMARY
        else
          echo "❌ Deployment to production failed!" >> $GITHUB_STEP_SUMMARY
        fi
        echo "- **Environment**: Production" >> $GITHUB_STEP_SUMMARY
        echo "- **Deployed SHA**: ${{ github.sha }}" >> $GITHUB_STEP_SUMMARY
        echo "- **Deployment Time**: $(date -u)" >> $GITHUB_STEP_SUMMARY
